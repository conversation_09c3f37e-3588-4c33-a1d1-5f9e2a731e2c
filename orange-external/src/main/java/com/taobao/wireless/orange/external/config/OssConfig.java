package com.taobao.wireless.orange.external.config;

import com.alibaba.normandy.credential.CredentialProvider;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OssConfig {
    @Autowired
    private CredentialProvider credentialProvider;

    @Value("${orange.oss.bucketName}")
    public String bucketName;

    @Value("${orange.oss.endpoint}")
    private String endpoint;

    @Value("${orange.oss.accessKeyId}")
    private String accessKeyId;

    @Value("${orange.oss.secretAccessKey}")
    private String secretAccessKey;

    @Bean
    public OSS ossClient() {
//        String resourceName = ResourceNames.ofAliyunOssBucketName(bucketName);
//        Credential credential = credentialProvider.getCredential(resourceName);
//        String accessKeyId = credential.getAccessKeyId();
//        String accessKeySecret = credential.getAccessKeySecret();

        return new OSSClientBuilder().build(endpoint, accessKeyId, secretAccessKey);
    }
}
