package com.taobao.wireless.orange.external.user;

import com.alibaba.ihr.amdplatform.service.param.DataQueryParam;
import com.alibaba.ihr.amdplatform.service.param.FilterField;
import com.taobao.wireless.orange.common.model.User;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class AmdpUserUtils {

    private static final String WORK_STATUS_ON_JOB = "A";

    public static DataQueryParam genQueryParam(Long combineId, AmdpUserQueryKeyEnum queryKey, Object queryValue) {
        FilterField filterField = new FilterField();
        filterField.setName(queryKey.getCode());
        filterField.setValue(queryValue);

        FilterField filterField2 = new FilterField();
        filterField2.setName(AmdpUserQueryKeyEnum.WORK_STATUS.getCode());
        filterField2.setValue(WORK_STATUS_ON_JOB);

        DataQueryParam queryParam = new DataQueryParam();
        queryParam.setCombineId(combineId);
        queryParam.setPageNo(1);
        queryParam.setPageSize(1);
        queryParam.setFilterFieldList(Arrays.asList(filterField, filterField2));

        return queryParam;
    }

    public static DataQueryParam genSearchQueryParam(Long combineId, String keyword, int pageSize) {
        FilterField filterField = new FilterField();
        filterField.setName(AmdpUserQueryKeyEnum.SEARCH_KEY.getCode());
        filterField.setValue(keyword);

        FilterField filterField2 = new FilterField();
        filterField2.setName(AmdpUserQueryKeyEnum.WORK_STATUS.getCode());
        filterField2.setValue(WORK_STATUS_ON_JOB);

        DataQueryParam queryParam = new DataQueryParam();
        queryParam.setCombineId(combineId);
        queryParam.setPageNo(1);
        queryParam.setPageSize(pageSize);
        queryParam.setFilterFieldList(Arrays.asList(filterField, filterField2));
        return queryParam;
    }

    public static User convert(AmdpUser from) {
        if (from == null) {
            return null;
        }
        AmdpUser.EmpEmployee empEmployee = from.getEmpEmployee();

        //用户存在且在职
        if (empEmployee != null && isOnTheJob(empEmployee.getWorkStatus())) {
            User user = new User();
            user.setId(empEmployee.getUserId());
            user.setEmpId(empEmployee.getWorkNo());
            user.setNickNameCn(empEmployee.getNickName());
            user.setEmailAddr(empEmployee.getBuMail());
            user.setEmailPrefix(empEmployee.getLoginAccount());
            user.setSupervisorEmpId(empEmployee.getSuperWorkNo());
            user.setName(empEmployee.getName());
            user.setHrStatus(empEmployee.getWorkStatus());
            if (StringUtils.isNotEmpty(user.getEmailAddr()) && StringUtils.isEmpty(user.getEmailPrefix())) {
                user.setEmailPrefix(StringUtils.substringBefore(user.getEmailAddr(), "@"));
            }
            if (StringUtils.isNotEmpty(user.getEmailPrefix()) && StringUtils.isEmpty(user.getEmailAddr())) {
                user.setEmailAddr(user.getEmailPrefix() + "@alibaba.com");
            }
            return user;
        } else {
            return null;
        }
    }

    public static List<User> convert(List<AmdpUser> list) {
        //用户存在且在职
        if (CollectionUtils.isNotEmpty(list)) {
            return list.stream()
                    .filter(item -> convert(item) != null)
                    .map(AmdpUserUtils::convert)
                    .collect(Collectors.toList());
        } else {
            return null;
        }
    }

    public static Map<String, User> convertMap(List<AmdpUser> list) {
        //用户存在且在职
        if (CollectionUtils.isNotEmpty(list)) {
            return list.stream()
                    .filter(item -> convert(item) != null)
                    .map(AmdpUserUtils::convert)
                    .collect(Collectors.toMap(User::getEmpId, x -> x));
        } else {
            return null;
        }
    }

    /**
     * 是否在职
     *
     * @param workStatus 工作状态
     * @return boolean value
     */
    private static boolean isOnTheJob(String workStatus) {
        return StringUtils.equalsIgnoreCase(workStatus, WORK_STATUS_ON_JOB);
    }
}
