package com.taobao.wireless.orange.external.diamond;

import java.io.ByteArrayInputStream;
import java.util.Properties;

import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.boot.diamond.annotation.DiamondListener;
import com.alibaba.boot.diamond.listener.DiamondDataCallback;
import org.springframework.core.env.Environment;
import org.springframework.core.env.PropertiesPropertySource;

/**
 * 通过 @DiamondListener注解，监听相关的配置项
 * 详见https://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-diamond
 */
@DiamondListener(dataId = "com.taobao.middleware:demoConfigFromListener.properties")
public class DiamondDataCallbackDemo implements DiamondDataCallback {

    @Autowired
    private ConfigBean configBean;

    @Autowired
    private Environment environment;

    private String dataCahe;

    public String getReceivedData() {
        return dataCahe;
    }

    @Override
    public void received(String data) {
        try {
            dataCahe = data;
            Properties properties = new Properties();
            properties.load(new ByteArrayInputStream(data.getBytes()));
            System.out.println("received from diamond listener: " + properties);

            // 把properties的值注入到ConfigBean里
            org.springframework.boot.context.properties.bind.Bindable<ConfigBean> bindable = org.springframework.boot.context.properties.bind.Bindable.ofInstance(configBean);
            org.springframework.boot.context.properties.bind.Binder binder = new org.springframework.boot.context.properties.bind.Binder(org.springframework.boot.context.properties.source.ConfigurationPropertySources.from(new PropertiesPropertySource("diamond-demo", properties)), new org.springframework.boot.context.properties.bind.PropertySourcesPlaceholdersResolver(this.environment));
            org.springframework.boot.context.properties.bind.BindResult<ConfigBean> result = binder.bind("", bindable);
            if (!result.isBound()) {
                System.out.println("Bind the data to configBean fail. Properties:" + properties);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

