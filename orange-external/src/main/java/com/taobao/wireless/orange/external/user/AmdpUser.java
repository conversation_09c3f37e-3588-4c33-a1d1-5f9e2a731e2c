package com.taobao.wireless.orange.external.user;

import com.alibaba.ihr.amdplatform.common.annotation.AmdpDomain;
import com.alibaba.ihr.amdplatform.common.annotation.AmdpDomainField;
import lombok.Data;

/***
 * 员工工号(workNo)部门编号(deptNo)花名(nickName)员工姓名(name)员工类型(empType)工作状态(workStatus)
 * 域账号(loginAccount)英文全名(englishName)中文名(firstNameCn)英文姓(lastNameEn)英文名(firstNameEn)(主职)
 * 实线主管工号(superWorkNo)(主职)实线主管雇佣编号(superOrderNum)(主职)实线主管姓名(superName)(主职)实线主管花名(superNickName)(主职)
 * Hrg工号(hrgWorkNo)(主职)hrg雇佣编号(hrgOrderNum)(主职)Hrg姓名(hrgName)(主职)Hrg花名(hrgNickName)
 * 公司邮箱(buMail)BUC.available(available)BUC.businessUnit(businessUnit)BUC.havanaEntities(havanaEntities)
 * BUC.primaryHavanaId(primaryHavanaId)BUC.userId(userId)BUC.userSign(userSign)BUC.userSignCode(userSignCode)
 * BUC.status(status)部门简称(主职)(deptShortName)部门名称(主职)(deptName)部门英文名称(主职)(deptEnName)
 */

@Data
public class AmdpUser {
    /**
     * 员工域
     */
    @AmdpDomain(
            code = "EMP_EMPLOYEE"
    )
    private EmpEmployee empEmployee;

    /**
     * 员工域
     */
    @AmdpDomain(
            code = "EMP_EMPLOYEE"
    )
    @Data
    public static class EmpEmployee {
        /**
         * 员工工号
         */
        @AmdpDomainField(
                code = "workNo",
                domainCode = "EMP_EMPLOYEE"
        )
        private String workNo;

        /**
         * 部门编号
         */
        @AmdpDomainField(
                code = "deptNo",
                domainCode = "EMP_EMPLOYEE"
        )
        private String deptNo;

        /**
         * 花名
         */
        @AmdpDomainField(
                code = "nickName",
                domainCode = "EMP_EMPLOYEE"
        )
        private String nickName;

        /**
         * 员工姓名
         */
        @AmdpDomainField(
                code = "name",
                domainCode = "EMP_EMPLOYEE"
        )
        private String name;

        /**
         * 员工类型
         */
        @AmdpDomainField(
                code = "empType",
                domainCode = "EMP_EMPLOYEE"
        )
        private String empType;

        /**
         * 工作状态
         */
        @AmdpDomainField(
                code = "workStatus",
                domainCode = "EMP_EMPLOYEE"
        )
        private String workStatus;

        /**
         * 域账号
         */
        @AmdpDomainField(
                code = "loginAccount",
                domainCode = "EMP_EMPLOYEE"
        )
        private String loginAccount;

        /**
         * 英文全名
         */
        @AmdpDomainField(
                code = "englishName",
                domainCode = "EMP_EMPLOYEE"
        )
        private String englishName;

        /**
         * 中文名
         */
        @AmdpDomainField(
                code = "firstNameCn",
                domainCode = "EMP_EMPLOYEE"
        )
        private String firstNameCn;

        /**
         * 英文姓
         */
        @AmdpDomainField(
                code = "lastNameEn",
                domainCode = "EMP_EMPLOYEE"
        )
        private String lastNameEn;

        /**
         * 英文名
         */
        @AmdpDomainField(
                code = "firstNameEn",
                domainCode = "EMP_EMPLOYEE"
        )
        private String firstNameEn;

        /**
         * (主职)实线主管工号
         */
        @AmdpDomainField(
                code = "superWorkNo",
                domainCode = "EMP_EMPLOYEE"
        )
        private String superWorkNo;

        /**
         * (主职)实线主管雇佣编号
         */
        @AmdpDomainField(
                code = "superOrderNum",
                domainCode = "EMP_EMPLOYEE"
        )
        private Long superOrderNum;

        /**
         * (主职)实线主管姓名
         */
        @AmdpDomainField(
                code = "superName",
                domainCode = "EMP_EMPLOYEE"
        )
        private String superName;

        /**
         * (主职)实线主管花名
         */
        @AmdpDomainField(
                code = "superNickName",
                domainCode = "EMP_EMPLOYEE"
        )
        private String superNickName;

        /**
         * (主职)Hrg工号
         */
        @AmdpDomainField(
                code = "hrgWorkNo",
                domainCode = "EMP_EMPLOYEE"
        )
        private String hrgWorkNo;

        /**
         * (主职)hrg雇佣编号
         */
        @AmdpDomainField(
                code = "hrgOrderNum",
                domainCode = "EMP_EMPLOYEE"
        )
        private Integer hrgOrderNum;

        /**
         * (主职)Hrg姓名
         */
        @AmdpDomainField(
                code = "hrgName",
                domainCode = "EMP_EMPLOYEE"
        )
        private String hrgName;

        /**
         * (主职)Hrg花名
         */
        @AmdpDomainField(
                code = "hrgNickName",
                domainCode = "EMP_EMPLOYEE"
        )
        private String hrgNickName;

        /**
         * 公司邮箱
         */
        @AmdpDomainField(
                code = "buMail",
                domainCode = "EMP_EMPLOYEE"
        )
        private String buMail;

        /**
         * BUC.available
         */
        @AmdpDomainField(
                code = "available",
                domainCode = "EMP_EMPLOYEE"
        )
        private String available;

        /**
         * BUC.businessUnit
         */
        @AmdpDomainField(
                code = "businessUnit",
                domainCode = "EMP_EMPLOYEE"
        )
        private String businessUnit;

        /**
         * BUC.havanaEntities
         */
        @AmdpDomainField(
                code = "havanaEntities",
                domainCode = "EMP_EMPLOYEE"
        )
        private String havanaEntities;

        /**
         * BUC.primaryHavanaId
         */
        @AmdpDomainField(
                code = "primaryHavanaId",
                domainCode = "EMP_EMPLOYEE"
        )
        private String primaryHavanaId;

        /**
         * BUC.userId
         */
        @AmdpDomainField(
                code = "userId",
                domainCode = "EMP_EMPLOYEE"
        )
        private Integer userId;

        /**
         * BUC.userSign
         */
        @AmdpDomainField(
                code = "userSign",
                domainCode = "EMP_EMPLOYEE"
        )
        private String userSign;

        /**
         * BUC.userSignCode
         */
        @AmdpDomainField(
                code = "userSignCode",
                domainCode = "EMP_EMPLOYEE"
        )
        private String userSignCode;

        /**
         * BUC.status
         */
        @AmdpDomainField(
                code = "status",
                domainCode = "EMP_EMPLOYEE"
        )
        private Integer status;

        /**
         * 部门简称(主职)
         */
        @AmdpDomainField(
                code = "deptShortName",
                domainCode = "EMP_EMPLOYEE"
        )
        private String deptShortName;

        /**
         * 部门名称(主职)
         */
        @AmdpDomainField(
                code = "deptName",
                domainCode = "EMP_EMPLOYEE"
        )
        private String deptName;

        /**
         * 部门英文名称(主职)
         */
        @AmdpDomainField(
                code = "deptEnName",
                domainCode = "EMP_EMPLOYEE"
        )
        private String deptEnName;
    }
}
