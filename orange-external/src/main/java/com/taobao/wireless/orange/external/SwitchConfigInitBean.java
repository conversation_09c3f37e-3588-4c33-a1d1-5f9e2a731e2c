package com.taobao.wireless.orange.external;

import com.taobao.csp.switchcenter.core.SwitchManager;
import com.taobao.wireless.orange.external.config.SwitchConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.BeanFactoryPostProcessor;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.core.PriorityOrdered;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class SwitchConfigInitBean implements BeanFactoryPostProcessor, PriorityOrdered {
    @Override
    public int getOrder() {
        return HIGHEST_PRECEDENCE;
    }

    @Override
    public void postProcessBeanFactory(ConfigurableListableBeanFactory beanFactory) {
        try {
            SwitchManager.init("orange", SwitchConfig.class);
        } catch (Exception e) {
            log.error("SwitchConfigInitBean init error.", e);
            System.exit(1);
        }
    }
}
