package com.taobao.wireless.orange.external;

import com.aliyun.oss.OSS;
import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.IOException;

@Service
@Slf4j
public class OssService {

    @Autowired
    private OSS ossClient;

    public byte[] readData(String bucketName, String objectName) {
        try {
            return ossClient.getObject(bucketName, objectName).getObjectContent().readAllBytes();
        } catch (IOException e) {
            log.error("OSS readData IOException", e);
            throw CommonException.getDynamicException(ExceptionEnum.OSS_READ_ERROR);
        }
    }

    public void uploadData(byte[] dataBytes, String bucketName, String objectName) {
        ossClient.putObject(bucketName, objectName, new ByteArrayInputStream(dataBytes));
    }

    public void uploadData(String data, String bucketName, String objectName) {
        ossClient.putObject(bucketName, objectName, new ByteArrayInputStream(data.getBytes()));
    }
}