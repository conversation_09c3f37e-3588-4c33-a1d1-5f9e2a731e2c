package com.taobao.wireless.orange.external.hsf;

import com.alibaba.boot.hsf.annotation.HSFConsumer;
import com.alibaba.ihr.amdplatform.service.api.AmdpDataQueryService;
import org.springframework.context.annotation.Configuration;

/**
 * hsf服务的统一个Config类，在其它需要使用的地方，直接@Autowired注入即可。详情见
 * https://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-hsf
 */
@Configuration
public class HsfConfig {
    @HSFConsumer(serviceVersion = "${amdp.hsf.version}", serviceGroup = "${amdp.hsf.group}", clientTimeout = 5000)
    private AmdpDataQueryService amdpDataQueryService;
}

