package com.taobao.wireless.orange.dal.enhanced.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 实验变体参数表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Getter
@Setter
@TableName("o_experiment_variant_parameter")
public class OExperimentVariantParameterDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 实验变体ID
     */
    @TableField("experiment_variant_id")
    private String experimentVariantId;

    /**
     * 实验ID
     */
    @TableField("experiment_id")
    private String experimentId;

    /**
     * 发布版本号
     */
    @TableField("release_version")
    private String releaseVersion;

    /**
     * 参数ID
     */
    @TableField("parameter_id")
    private String parameterId;

    /**
     * 参数键名
     */
    @TableField("parameter_key")
    private String parameterKey;

    /**
     * 参数值
     */
    @TableField("value")
    private String value;

    /**
     * 状态
     */
    @TableField("status")
    private String status;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified", fill = FieldFill.INSERT_UPDATE)
    private Date gmtModified;

    /**
     * 创建者
     */
    @TableField(value = "creator", fill = FieldFill.INSERT)
    private String creator;

    /**
     * 修改者
     */
    @TableField(value = "modifier", fill = FieldFill.INSERT_UPDATE)
    private String modifier;
}
