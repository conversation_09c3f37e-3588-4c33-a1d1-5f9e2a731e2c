package com.taobao.wireless.orange.dal.base.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 探针描述
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Getter
@Setter
@TableName("orange_probe")
public class ProbeDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified", fill = FieldFill.INSERT_UPDATE)
    private Date gmtModified;

    /**
     * 有效
     */
    @TableField("is_available")
    private String isAvailable;

    /**
     * appKey
     */
    @TableField("app_key")
    private String appKey;

    /**
     * 探针描述
     */
    @TableField("metas")
    private String metas;

    /**
     * 索引版本
     */
    @TableField("index_version")
    private String indexVersion;

    /**
     * 最大变更版本
     */
    @TableField("change_version")
    private String changeVersion;

    /**
     * 发布类型：GRAY/PUBLISH
     */
    @TableField("publish_type")
    private String publishType;
}
