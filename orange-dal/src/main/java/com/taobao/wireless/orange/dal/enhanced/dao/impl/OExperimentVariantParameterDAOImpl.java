package com.taobao.wireless.orange.dal.enhanced.dao.impl;

import com.taobao.wireless.orange.dal.enhanced.entity.OExperimentVariantParameterDO;
import com.taobao.wireless.orange.dal.enhanced.mapper.OExperimentVariantParameterMapper;
import com.taobao.wireless.orange.dal.enhanced.dao.OExperimentVariantParameterDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 实验变体参数表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Service
public class OExperimentVariantParameterDAOImpl extends ServiceImpl<OExperimentVariantParameterMapper, OExperimentVariantParameterDO> implements OExperimentVariantParameterDAO {

}
