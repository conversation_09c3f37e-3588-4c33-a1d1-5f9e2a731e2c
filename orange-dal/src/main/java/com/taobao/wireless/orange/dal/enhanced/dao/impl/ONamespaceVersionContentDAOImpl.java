package com.taobao.wireless.orange.dal.enhanced.dao.impl;

import com.taobao.wireless.orange.dal.enhanced.entity.ONamespaceVersionContentDO;
import com.taobao.wireless.orange.dal.enhanced.mapper.ONamespaceVersionContentMapper;
import com.taobao.wireless.orange.dal.enhanced.dao.ONamespaceVersionContentDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 命名空间版本内容表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Service
public class ONamespaceVersionContentDAOImpl extends ServiceImpl<ONamespaceVersionContentMapper, ONamespaceVersionContentDO> implements ONamespaceVersionContentDAO {

}
