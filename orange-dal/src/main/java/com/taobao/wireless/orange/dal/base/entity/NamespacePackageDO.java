package com.taobao.wireless.orange.dal.base.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 聚合配置映射表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Getter
@Setter
@TableName("orange_namespace_package")
public class NamespacePackageDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified", fill = FieldFill.INSERT_UPDATE)
    private Date gmtModified;

    /**
     * 属于哪个app
     */
    @TableField("app_key")
    private String appKey;

    /**
     * 属于哪个app版本
     */
    @TableField("app_version")
    private String appVersion;

    /**
     * 资源文件集合的md5
     */
    @TableField("md5")
    private String md5;

    /**
     * resource_id
     */
    @TableField("resource_id")
    private String resourceId;

    /**
     * 是否可用
     */
    @TableField("is_available")
    private String isAvailable;

    /**
     * 加载级别
     */
    @TableField("load_level")
    private Integer loadLevel;

    /**
     * 打包id
     */
    @TableField("package_id")
    private String packageId;

    /**
     * 内容校验码
     */
    @TableField("check_code")
    private String checkCode;
}
