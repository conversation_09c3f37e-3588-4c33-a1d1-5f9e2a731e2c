package com.taobao.wireless.orange.dal.base.dao.impl;

import com.taobao.wireless.orange.dal.base.entity.AppDO;
import com.taobao.wireless.orange.dal.base.mapper.AppMapper;
import com.taobao.wireless.orange.dal.base.dao.AppDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 该系统接入的业务方 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Service
public class AppDAOImpl extends ServiceImpl<AppMapper, AppDO> implements AppDAO {

}
