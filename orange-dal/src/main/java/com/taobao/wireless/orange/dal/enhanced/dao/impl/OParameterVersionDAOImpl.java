package com.taobao.wireless.orange.dal.enhanced.dao.impl;

import com.taobao.wireless.orange.dal.enhanced.entity.OParameterVersionDO;
import com.taobao.wireless.orange.dal.enhanced.mapper.OParameterVersionMapper;
import com.taobao.wireless.orange.dal.enhanced.dao.OParameterVersionDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 参数版本表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Service
public class OParameterVersionDAOImpl extends ServiceImpl<OParameterVersionMapper, OParameterVersionDO> implements OParameterVersionDAO {

}
