package com.taobao.wireless.orange.dal.base.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 所有的命名空间定义
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Getter
@Setter
@TableName("orange_namespace")
public class NamespaceDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified", fill = FieldFill.INSERT_UPDATE)
    private Date gmtModified;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 类型
     */
    @TableField("type")
    private Integer type;

    /**
     * 属于哪个app或者属于哪个分组
     */
    @TableField("app_key_or_group")
    private String appKeyOrGroup;

    /**
     * 命名空间id
     */
    @TableField("namespace_id")
    private String namespaceId;

    /**
     * is_available
     */
    @TableField("is_available")
    private String isAvailable;

    /**
     * 该命名空间的加载级别，越高客户端越要提前加载
     */
    @TableField("load_level")
    private Integer loadLevel;

    /**
     * 命名空间的owners
     */
    @TableField("owners")
    private String owners;

    /**
     * 废弃
     */
    @TableField("reviewers")
    private String reviewers;

    /**
     * 描述
     */
    @TableField("detail")
    private String detail;

    /**
     * 废弃
     */
    @TableField("developers")
    private String developers;

    /**
     * 测试
     */
    @TableField("testers")
    private String testers;

    /**
     * 已废弃
     */
    @TableField("target_app_version")
    private String targetAppVersion;

    /**
     * 创建者
     */
    @TableField(value = "creator", fill = FieldFill.INSERT)
    private String creator;

    /**
     * 下线操作者
     */
    @TableField("offline_operator")
    private String offlineOperator;

    /**
     * 重新上线操作者
     */
    @TableField("online_operator")
    private String onlineOperator;

    /**
     * 审核标志
     */
    @TableField("auditing_flag")
    private String auditingFlag;

    /**
     * 配置子类型
     */
    @TableField("sub_type")
    private Integer subType;

    /**
     * 摩天轮模块ID列表(,分隔)
     */
    @TableField("modules")
    private String modules;
}
