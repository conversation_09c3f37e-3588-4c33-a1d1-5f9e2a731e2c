package com.taobao.wireless.orange.dal.enhanced.dao.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.taobao.wireless.orange.common.constant.enums.ParameterStatus;
import com.taobao.wireless.orange.dal.enhanced.dao.OConditionDAO;
import com.taobao.wireless.orange.dal.enhanced.entity.OConditionDO;
import com.taobao.wireless.orange.dal.enhanced.mapper.OConditionMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 条件表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Service
public class OConditionDAOImpl extends ServiceImpl<OConditionMapper, OConditionDO> implements OConditionDAO {

    @Override
    public List<OConditionDO> getByNames(String namespaceId, List<String> conditionNames) {
        return this.lambdaQuery()
                .eq(OConditionDO::getNamespaceId, namespaceId)
                .in(OConditionDO::getName, conditionNames)
                .ne(OConditionDO::getStatus, ParameterStatus.DELETED)
                .list();
    }

    @Override
    public OConditionDO getByConditionId(String conditionId) {
        return this.lambdaQuery()
                .eq(OConditionDO::getConditionId, conditionId)
                .ne(OConditionDO::getStatus, ParameterStatus.DELETED)
                .one();
    }
}
