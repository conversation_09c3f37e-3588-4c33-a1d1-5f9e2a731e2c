package com.taobao.wireless.orange.dal.base.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 配置版本灰度记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Getter
@Setter
@TableName("orange_namespace_gray_record")
public class NamespaceGrayRecordDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 命名空间ID
     */
    @TableField("namespace_id")
    private String namespaceId;

    /**
     * appKey
     */
    @TableField("app_key")
    private String appKey;

    /**
     * 命名空间名称
     */
    @TableField("namespace_name")
    private String namespaceName;

    /**
     * 版本表版本
     */
    @TableField("version")
    private String version;

    /**
     * 变更ID
     */
    @TableField("change_version")
    private String changeVersion;

    /**
     * 灰度比例（以万分之一为单位）
     */
    @TableField("gray_ratio")
    private Integer grayRatio;

    /**
     * 灰度完整描述
     */
    @TableField("gray_metas")
    private String grayMetas;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified", fill = FieldFill.INSERT_UPDATE)
    private Date gmtModified;

    /**
     * 状态
     */
    @TableField("status")
    private String status;
}
