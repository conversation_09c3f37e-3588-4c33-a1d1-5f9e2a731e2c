package com.taobao.wireless.orange.dal.base.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 发布单每15分钟的增量ack数量
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Getter
@Setter
@TableName("orange_ack_namespace_version_inc_15m")
public class AckNamespaceVersionInc15mDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * namespace_version,发布单的版本
     */
    @TableField("namespace_version")
    private String namespaceVersion;

    /**
     * 独立设备数
     */
    @TableField("cnt")
    private Long cnt;

    /**
     * 生成日期
     */
    @TableField("gmtcreate")
    private Date gmtcreate;
}
