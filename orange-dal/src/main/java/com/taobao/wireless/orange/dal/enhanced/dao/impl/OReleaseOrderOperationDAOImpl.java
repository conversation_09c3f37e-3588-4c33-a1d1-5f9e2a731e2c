package com.taobao.wireless.orange.dal.enhanced.dao.impl;

import com.taobao.wireless.orange.dal.enhanced.entity.OReleaseOrderOperationDO;
import com.taobao.wireless.orange.dal.enhanced.mapper.OReleaseOrderOperationMapper;
import com.taobao.wireless.orange.dal.enhanced.dao.OReleaseOrderOperationDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 发布单操作表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Service
public class OReleaseOrderOperationDAOImpl extends ServiceImpl<OReleaseOrderOperationMapper, OReleaseOrderOperationDO> implements OReleaseOrderOperationDAO {

}
