package com.taobao.wireless.orange.dal.base.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 配置索引
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Getter
@Setter
@TableName("orange_index")
public class IndexDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified", fill = FieldFill.INSERT_UPDATE)
    private Date gmtModified;

    /**
     * 是否可用
     */
    @TableField("is_available")
    private String isAvailable;

    /**
     * resource_id
     */
    @TableField("resource_id")
    private String resourceId;

    /**
     * 属于哪个app
     */
    @TableField("app_key")
    private String appKey;

    /**
     * 属于哪个app版本
     */
    @TableField("app_version")
    private String appVersion;

    /**
     * 索引id，不可重复
     */
    @TableField("index_id")
    private String indexId;

    /**
     * 索引类型
     */
    @TableField("type")
    private Integer type;

    /**
     * 资源文件的md5
     */
    @TableField("md5")
    private String md5;

    /**
     * 版本
     */
    @TableField("version")
    private String version;

    /**
     * 索引生成时间打点
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 如果是合并的索引，这个索引内部使用到的appIndex的版本
     */
    @TableField("app_index_version")
    private String appIndexVersion;

    /**
     * 如果是合并的索引，这个索引内部使用到的versionIndex的版本
     */
    @TableField("version_index_version")
    private String versionIndexVersion;

    /**
     * 全量配置数
     */
    @TableField("total_cnt")
    private Integer totalCnt;

    /**
     * 发布类型：GRAY/PUBLISH
     */
    @TableField("publish_type")
    private String publishType;
}
