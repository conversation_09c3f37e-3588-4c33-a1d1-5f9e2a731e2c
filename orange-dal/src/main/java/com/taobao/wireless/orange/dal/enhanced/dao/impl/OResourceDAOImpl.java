package com.taobao.wireless.orange.dal.enhanced.dao.impl;

import com.taobao.wireless.orange.dal.enhanced.entity.OResourceDO;
import com.taobao.wireless.orange.dal.enhanced.mapper.OResourceMapper;
import com.taobao.wireless.orange.dal.enhanced.dao.OResourceDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 配置中心的数据，在数据库中存一份 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-06
 */
@Service
public class OResourceDAOImpl extends ServiceImpl<OResourceMapper, OResourceDO> implements OResourceDAO {

}
