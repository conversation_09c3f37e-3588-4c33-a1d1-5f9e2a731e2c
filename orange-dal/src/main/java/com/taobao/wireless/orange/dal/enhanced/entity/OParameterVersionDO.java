package com.taobao.wireless.orange.dal.enhanced.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.taobao.wireless.orange.common.constant.enums.ChangeType;
import com.taobao.wireless.orange.common.constant.enums.ParameterValueType;
import com.taobao.wireless.orange.common.constant.enums.VersionStatus;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 参数版本表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Getter
@Setter
@TableName("o_parameter_version")
public class OParameterVersionDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 参数ID
     */
    @TableField("parameter_id")
    private String parameterId;

    /**
     * 发布版本号
     */
    @TableField("release_version")
    private String releaseVersion;

    /**
     * 应用KEY
     */
    @TableField("app_key")
    private String appKey;

    /**
     * 命名空间ID
     */
    @TableField("namespace_id")
    private String namespaceId;

    /**
     * 参数键名
     */
    @TableField("parameter_key")
    private String parameterKey;

    /**
     * 参数值类型
     */
    @TableField("value_type")
    private ParameterValueType valueType;

    /**
     * 条件顺序
     */
    @TableField("conditions_order")
    private String conditionsOrder;

    /**
     * 修改前发布版本号
     */
    @TableField("previous_release_version")
    private String previousReleaseVersion;

    /**
     * 变更类型
     */
    @TableField("change_type")
    private ChangeType changeType;

    /**
     * 状态
     */
    @TableField("status")
    private VersionStatus status;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified", fill = FieldFill.INSERT_UPDATE)
    private Date gmtModified;

    /**
     * 创建者
     */
    @TableField(value = "creator", fill = FieldFill.INSERT)
    private String creator;

    /**
     * 修改者
     */
    @TableField(value = "modifier", fill = FieldFill.INSERT_UPDATE)
    private String modifier;
}
