package com.taobao.wireless.orange.dal.base.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 该系统接入的业务方
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Getter
@Setter
@TableName("orange_app")
public class AppDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified", fill = FieldFill.INSERT_UPDATE)
    private Date gmtModified;

    /**
     * 是否可用
     */
    @TableField("is_available")
    private String isAvailable;

    /**
     * 业务方当前环境appkey，唯一建不可重复
     */
    @TableField("app_key")
    private String appKey;

    /**
     * 业务方owners
     */
    @TableField("app_owners")
    private String appOwners;

    /**
     * 业务描述
     */
    @TableField("app_detail")
    private String appDetail;

    /**
     * 业务名称
     */
    @TableField("app_name")
    private String appName;

    /**
     * 废弃
     */
    @TableField("pre_app_key")
    private String preAppKey;

    /**
     * 废弃
     */
    @TableField("online_app_key")
    private String onlineAppKey;

    /**
     * 废弃
     */
    @TableField("daily_app_key")
    private String dailyAppKey;

    /**
     * 产品唯一标示
     */
    @TableField("app_mark")
    private String appMark;

    /**
     * 产品包名
     */
    @TableField("app_package_name")
    private String appPackageName;
}
