package com.taobao.wireless.orange.dal.base.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 活跃用户数快照
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Getter
@Setter
@TableName("orange_cfg_update_rate")
public class CfgUpdateRateDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified", fill = FieldFill.INSERT_UPDATE)
    private Date gmtModified;

    /**
     * appKey
     */
    @TableField("app_key")
    private String appKey;

    /**
     * 名称
     */
    @TableField("namespace_id")
    private String namespaceId;

    /**
     * 发布版本
     */
    @TableField("version")
    private String version;

    /**
     * 当前周期
     */
    @TableField("duration")
    private Long duration;

    /**
     * 更新数
     */
    @TableField("update_num")
    private Long updateNum;

    /**
     * 活跃用户数
     */
    @TableField("active_user_num")
    private Long activeUserNum;

    /**
     * 数据状态
     */
    @TableField("status")
    private Integer status;
}
