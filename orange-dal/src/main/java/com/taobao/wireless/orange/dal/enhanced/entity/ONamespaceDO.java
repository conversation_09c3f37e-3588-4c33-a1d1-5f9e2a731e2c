package com.taobao.wireless.orange.dal.enhanced.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.taobao.wireless.orange.common.constant.enums.NamespaceBizType;
import com.taobao.wireless.orange.common.constant.enums.NamespaceStatus;
import com.taobao.wireless.orange.dal.handler.ListTypeHandler;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 命名空间表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-15
 */
@Getter
@Setter
@TableName("o_namespace")
public class ONamespaceDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 命名空间ID
     */
    @TableField("namespace_id")
    private String namespaceId;

    /**
     * 应用KEY
     */
    @TableField("app_key")
    private String appKey;

    /**
     * 命名空间的类型
     */
    @TableField(value = "biz_type")
    private NamespaceBizType bizType;

    /**
     * 命名空间类型对应的实体ID
     */
    @TableField("biz_id")
    private String bizId;

    /**
     * 负责人列表
     */
    @TableField(value = "owners", typeHandler = ListTypeHandler.class)
    private List<String> owners;

    /**
     * 测试人员列表
     */
    @TableField(value = "testers", typeHandler = ListTypeHandler.class)
    private List<String> testers;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified", fill = FieldFill.INSERT_UPDATE)
    private Date gmtModified;

    /**
     * 创建者
     */
    @TableField(value = "creator", fill = FieldFill.INSERT)
    private String creator;

    /**
     * 修改者
     */
    @TableField(value = "modifier", fill = FieldFill.INSERT_UPDATE)
    private String modifier;

    /**
     * 状态
     */
    @TableField("status")
    private NamespaceStatus status;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 描述
     */
    @TableField("description")
    private String description;
}
