package com.taobao.wireless.orange.dal.enhanced.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.taobao.wireless.orange.common.constant.enums.OperationStatus;
import com.taobao.wireless.orange.common.constant.enums.OperationType;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 发布单操作表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Getter
@Setter
@TableName("o_release_order_operation")
public class OReleaseOrderOperationDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 应用KEY
     */
    @TableField("app_key")
    private String appKey;

    /**
     * 命名空间ID
     */
    @TableField("namespace_id")
    private String namespaceId;

    /**
     * 发布版本号
     */
    @TableField("release_version")
    private String releaseVersion;

    /**
     * 操作类型
     */
    @TableField("type")
    private OperationType type;

    /**
     * 输入参数(JSON)
     */
    @TableField("params")
    private String params;

    /**
     * 结果信息(JSON)
     */
    @TableField("result")
    private String result;

    /**
     * 操作状态
     */
    @TableField("status")
    private OperationStatus status;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified", fill = FieldFill.INSERT_UPDATE)
    private Date gmtModified;

    /**
     * 创建者
     */
    @TableField(value = "creator", fill = FieldFill.INSERT)
    private String creator;

    /**
     * 修改者
     */
    @TableField(value = "modifier", fill = FieldFill.INSERT_UPDATE)
    private String modifier;
}
