package com.taobao.wireless.orange.dal.config;


import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.autoconfigure.ConfigurationCustomizer;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.config.GlobalConfig;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.taobao.tddl.group.jdbc.TGroupDataSource;
import com.taobao.wireless.orange.dal.handler.CustomMetaObjectHandler;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;

@Configuration
@MapperScan(value = "com.taobao.wireless.orange.dal.enhanced.mapper", sqlSessionFactoryRef = "OrangeConfigFactory")
public class EnhancedDatabaseConfig {

    @Autowired
    private CustomMetaObjectHandler customMetaObjectHandler;

    @Bean(name = "OrangeConfigDataSource")
    @Primary
    public DataSource dataSource() {
        TGroupDataSource dataSource = new TGroupDataSource();
        dataSource.setAppName("ORANGE_CONFIG_APP");
        dataSource.setDbGroupKey("ORANGE_CONFIG_GROUP");
        dataSource.init();
        return dataSource;
    }

    @Bean(name = "OrangeConfigFactory")
    @Primary
    public SqlSessionFactory sqlSessionFactory(@Qualifier("OrangeConfigDataSource") DataSource dataSource) throws Exception {
        MybatisSqlSessionFactoryBean sqlSessionFactoryBean = new MybatisSqlSessionFactoryBean();
        sqlSessionFactoryBean.setDataSource(dataSource);
        sqlSessionFactoryBean.setTypeAliasesPackage("com.taobao.wireless.orange.dal.enhanced.model");
        sqlSessionFactoryBean.setTypeHandlersPackage("com.taobao.wireless.orange.dal.handler");
        sqlSessionFactoryBean.setPlugins(mybatisPlusInterceptor());

        GlobalConfig globalConfig = new GlobalConfig();
        globalConfig.setMetaObjectHandler(customMetaObjectHandler);
        sqlSessionFactoryBean.setGlobalConfig(globalConfig);
        return sqlSessionFactoryBean.getObject();
    }

    @Bean("OrangeConfigSqlSessionTemplate")
    public SqlSessionTemplate sqlSessionTemplate(@Qualifier("OrangeConfigFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

    @Bean
    public ConfigurationCustomizer configurationCustomizer() {
        return new ConfigurationCustomizer() {
            @Override
            public void customize(MybatisConfiguration configuration) {
                configuration.setMapUnderscoreToCamelCase(true);
            }
        };
    }

    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        return interceptor;
    }
}
