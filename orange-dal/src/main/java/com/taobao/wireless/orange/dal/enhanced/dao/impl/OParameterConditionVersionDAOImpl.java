package com.taobao.wireless.orange.dal.enhanced.dao.impl;

import com.taobao.wireless.orange.dal.enhanced.entity.OParameterConditionVersionDO;
import com.taobao.wireless.orange.dal.enhanced.mapper.OParameterConditionVersionMapper;
import com.taobao.wireless.orange.dal.enhanced.dao.OParameterConditionVersionDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 参数条件版本表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Service
public class OParameterConditionVersionDAOImpl extends ServiceImpl<OParameterConditionVersionMapper, OParameterConditionVersionDO> implements OParameterConditionVersionDAO {

}
