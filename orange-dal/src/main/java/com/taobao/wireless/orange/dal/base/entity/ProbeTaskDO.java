package com.taobao.wireless.orange.dal.base.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 探针推送任务
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Getter
@Setter
@TableName("orange_probe_task")
public class ProbeTaskDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified", fill = FieldFill.INSERT_UPDATE)
    private Date gmtModified;

    /**
     * 是否可用
     */
    @TableField("is_available")
    private String isAvailable;

    /**
     * appKey
     */
    @TableField("app_key")
    private String appKey;

    /**
     * 名称
     */
    @TableField("namespace")
    private String namespace;

    /**
     * 是否紧急发布
     */
    @TableField("is_emergent")
    private String isEmergent;

    /**
     * 发布版本
     */
    @TableField("version")
    private String version;

    /**
     * wmcc的任务ID
     */
    @TableField("wmcc_id")
    private Long wmccId;

    /**
     * 新版探针任务ID
     */
    @TableField("wmcc_id2")
    private Long wmccId2;

    /**
     * 变更类型:1-发布，2-回滚
     */
    @TableField("change_type")
    private Integer changeType;

    /**
     * 新版包含灰度探针任务ID
     */
    @TableField("wmcc_id3")
    private Long wmccId3;
}
