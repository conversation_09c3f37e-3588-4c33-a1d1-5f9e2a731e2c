package com.taobao.wireless.orange.dal.enhanced.dao.impl;

import com.taobao.wireless.orange.dal.enhanced.entity.OConditionVersionDO;
import com.taobao.wireless.orange.dal.enhanced.mapper.OConditionVersionMapper;
import com.taobao.wireless.orange.dal.enhanced.dao.OConditionVersionDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 条件版本表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Service
public class OConditionVersionDAOImpl extends ServiceImpl<OConditionVersionMapper, OConditionVersionDO> implements OConditionVersionDAO {

}
