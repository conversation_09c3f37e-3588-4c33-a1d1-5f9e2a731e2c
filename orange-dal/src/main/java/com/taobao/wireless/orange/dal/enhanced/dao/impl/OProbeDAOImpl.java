package com.taobao.wireless.orange.dal.enhanced.dao.impl;

import com.taobao.wireless.orange.dal.enhanced.entity.OProbeDO;
import com.taobao.wireless.orange.dal.enhanced.mapper.OProbeMapper;
import com.taobao.wireless.orange.dal.enhanced.dao.OProbeDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 探针表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Service
public class OProbeDAOImpl extends ServiceImpl<OProbeMapper, OProbeDO> implements OProbeDAO {

}
