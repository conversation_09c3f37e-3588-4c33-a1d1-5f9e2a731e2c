package com.taobao.wireless.orange.dal.base.dao.impl;

import com.taobao.wireless.orange.dal.base.entity.AckNamespaceVersionIncDailyDO;
import com.taobao.wireless.orange.dal.base.mapper.AckNamespaceVersionIncDailyMapper;
import com.taobao.wireless.orange.dal.base.dao.AckNamespaceVersionIncDailyDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 发布单每日增量统计表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Service
public class AckNamespaceVersionIncDailyDAOImpl extends ServiceImpl<AckNamespaceVersionIncDailyMapper, AckNamespaceVersionIncDailyDO> implements AckNamespaceVersionIncDailyDAO {

}
