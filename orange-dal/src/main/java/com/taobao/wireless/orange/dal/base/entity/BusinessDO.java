package com.taobao.wireless.orange.dal.base.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * orange业务方使用表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Getter
@Setter
@TableName("orange_business")
public class BusinessDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified", fill = FieldFill.INSERT_UPDATE)
    private Date gmtModified;

    /**
     * aone应用名
     */
    @TableField("name")
    private String name;

    /**
     * 创建人工号
     */
    @TableField(value = "creator", fill = FieldFill.INSERT)
    private String creator;

    /**
     * 负责人工号
     */
    @TableField("owners")
    private String owners;

    /**
     * 状态，0- 成功 1- 申请中 2- 审核通过 3- 审核失败 4-已下线
     */
    @TableField("status")
    private Integer status;

    /**
     * 是否可用
     */
    @TableField("is_available")
    private String isAvailable;

    /**
     * 授权码
     */
    @TableField("token")
    private String token;

    /**
     * 权限信息
     */
    @TableField("permissions")
    private String permissions;

    /**
     * 描述信息
     */
    @TableField("memo")
    private String memo;

    /**
     * 修改者
     */
    @TableField(value = "modifier", fill = FieldFill.INSERT_UPDATE)
    private String modifier;
}
