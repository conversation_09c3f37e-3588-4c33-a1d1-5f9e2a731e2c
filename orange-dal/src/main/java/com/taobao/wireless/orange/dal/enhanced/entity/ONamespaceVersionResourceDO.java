package com.taobao.wireless.orange.dal.enhanced.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 命名空间版本资源表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Getter
@Setter
@TableName("o_namespace_version_resource")
public class ONamespaceVersionResourceDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 应用KEY
     */
    @TableField("app_key")
    private String appKey;

    /**
     * 命名空间ID
     */
    @TableField("namespace_id")
    private String namespaceId;

    /**
     * 命名空间正式版本号
     */
    @TableField("namespace_version")
    private String namespaceVersion;

    /**
     * 命名空间变更版本号
     */
    @TableField("namespace_change_version")
    private String namespaceChangeVersion;

    /**
     * 正式内容文件地址
     */
    @TableField("resource_id")
    private String resourceId;

    /**
     * 灰度内容文件地址
     */
    @TableField("rollout_resource_id")
    private String rolloutResourceId;

    /**
     * 实验内容文件地址
     */
    @TableField("experiment_resource_id")
    private String experimentResourceId;

    /**
     * 差量内容的基线索引版本
     */
    @TableField("base_index_version")
    private String baseIndexVersion;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified", fill = FieldFill.INSERT_UPDATE)
    private Date gmtModified;
}
