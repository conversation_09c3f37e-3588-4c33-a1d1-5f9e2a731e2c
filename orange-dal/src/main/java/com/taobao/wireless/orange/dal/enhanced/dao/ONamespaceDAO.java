package com.taobao.wireless.orange.dal.enhanced.dao;

import com.taobao.wireless.orange.dal.enhanced.entity.ONamespaceDO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 命名空间表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-15
 */
public interface ONamespaceDAO extends IService<ONamespaceDO> {
    public ONamespaceDO getByNamespaceId(String namespaceId);
    public boolean updateByNamespaceId(ONamespaceDO namespaceDO);
}
