package com.taobao.wireless.orange.dal.base.dao.impl;

import com.taobao.wireless.orange.dal.base.entity.AppVersionDO;
import com.taobao.wireless.orange.dal.base.mapper.AppVersionMapper;
import com.taobao.wireless.orange.dal.base.dao.AppVersionDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * app版本列表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Service
public class AppVersionDAOImpl extends ServiceImpl<AppVersionMapper, AppVersionDO> implements AppVersionDAO {

}
