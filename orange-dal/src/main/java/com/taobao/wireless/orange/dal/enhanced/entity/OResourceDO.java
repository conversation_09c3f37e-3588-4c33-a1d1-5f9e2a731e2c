package com.taobao.wireless.orange.dal.enhanced.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.taobao.wireless.orange.common.constant.enums.ResourceType;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 配置中心的数据，在数据库中存一份
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-06
 */
@Getter
@Setter
@TableName("o_resource")
public class OResourceDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * resourceId
     */
    @TableField("resource_id")
    private String resourceId;

    /**
     * data
     */
    @TableField("data")
    private String data;

    /**
     * MD5
     */
    @TableField("md5")
    private String md5;

    /**
     * 内容类型
     */
    @TableField("type")
    private ResourceType type;

    /**
     * 原始数据
     */
    @TableField("src_content")
    private String srcContent;

    /**
     * appKey
     */
    @TableField("app_key")
    private String appKey;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified", fill = FieldFill.INSERT_UPDATE)
    private Date gmtModified;
}
