package com.taobao.wireless.orange.dal.base.dao.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.taobao.wireless.orange.dal.base.dao.NamespaceDAO;
import com.taobao.wireless.orange.dal.base.entity.NamespaceDO;
import com.taobao.wireless.orange.dal.base.mapper.NamespaceMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 所有的命名空间定义 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Service
public class NamespaceDAOImpl extends ServiceImpl<NamespaceMapper, NamespaceDO> implements NamespaceDAO {
}
