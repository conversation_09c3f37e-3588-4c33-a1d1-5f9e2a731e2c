package com.taobao.wireless.orange.dal.enhanced.dao.impl;

import com.taobao.wireless.orange.dal.enhanced.entity.OExperimentVariantDO;
import com.taobao.wireless.orange.dal.enhanced.mapper.OExperimentVariantMapper;
import com.taobao.wireless.orange.dal.enhanced.dao.OExperimentVariantDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 实验变体表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Service
public class OExperimentVariantDAOImpl extends ServiceImpl<OExperimentVariantMapper, OExperimentVariantDO> implements OExperimentVariantDAO {

}
