package com.taobao.wireless.orange.dal.base.dao.impl;

import com.taobao.wireless.orange.dal.base.entity.NamespaceVersionDO;
import com.taobao.wireless.orange.dal.base.mapper.NamespaceVersionMapper;
import com.taobao.wireless.orange.dal.base.dao.NamespaceVersionDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 命名空间版本详情 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Service
public class NamespaceVersionDAOImpl extends ServiceImpl<NamespaceVersionMapper, NamespaceVersionDO> implements NamespaceVersionDAO {

}
