package com.taobao.wireless.orange.dal.config;


import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.taobao.tddl.group.jdbc.TGroupDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

@Configuration
@MapperScan(value = "com.taobao.wireless.orange.dal.base.mapper", sqlSessionFactoryRef = "WirelessOrangeFactory")
public class BaseDatabaseConfig {
    @Bean(name = "WirelessOrangeDataSource")
    public DataSource dataSource() {
        TGroupDataSource dataSource = new TGroupDataSource();
        dataSource.setAppName("WIRELESS_ORANGE_APP");
        dataSource.setDbGroupKey("WIRELESS_ORANGE_GROUP");
        dataSource.init();
        return dataSource;
    }

    @Bean(name = "WirelessOrangeFactory")
    public SqlSessionFactory sqlSessionFactory(@Qualifier("WirelessOrangeDataSource") DataSource dataSource) throws Exception {
        MybatisSqlSessionFactoryBean sqlSessionFactoryBean = new MybatisSqlSessionFactoryBean();
        sqlSessionFactoryBean.setDataSource(dataSource);
        sqlSessionFactoryBean.setTypeAliasesPackage("com.taobao.wireless.orange.dal.base.model");
        return sqlSessionFactoryBean.getObject();
    }

    @Bean("WirelessOrangeSqlSessionTemplate")
    //同理，参数必须使用Qualifier注解来指定使用哪个SqlSessionFactory bean对象
    public SqlSessionTemplate sqlSessionTemplate(@Qualifier("WirelessOrangeFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }
}
