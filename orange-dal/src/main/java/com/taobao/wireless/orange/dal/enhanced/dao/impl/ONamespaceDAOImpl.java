package com.taobao.wireless.orange.dal.enhanced.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.taobao.wireless.orange.common.constant.enums.NamespaceStatus;
import com.taobao.wireless.orange.dal.enhanced.dao.ONamespaceDAO;
import com.taobao.wireless.orange.dal.enhanced.entity.ONamespaceDO;
import com.taobao.wireless.orange.dal.enhanced.mapper.ONamespaceMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 命名空间表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-15
 */
@Service
public class ONamespaceDAOImpl extends ServiceImpl<ONamespaceMapper, ONamespaceDO> implements ONamespaceDAO {

    @Override
    public ONamespaceDO getByNamespaceId(String namespaceId) {
        return this.lambdaQuery().eq(ONamespaceDO::getNamespaceId, namespaceId).ne(ONamespaceDO::getStatus, NamespaceStatus.DELETE).one();
    }

    @Override
    public boolean updateByNamespaceId(ONamespaceDO namespaceDO) {
        return this.update(namespaceDO, new LambdaQueryWrapper<ONamespaceDO>().eq(ONamespaceDO::getNamespaceId, namespaceDO.getNamespaceId()));
    }
}
