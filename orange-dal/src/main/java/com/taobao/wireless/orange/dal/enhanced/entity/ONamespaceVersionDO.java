package com.taobao.wireless.orange.dal.enhanced.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.taobao.wireless.orange.common.constant.enums.Available;
import com.taobao.wireless.orange.common.constant.enums.NamespaceVersionChangeType;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 命名空间版本表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
@Getter
@Setter
@TableName("o_namespace_version")
public class ONamespaceVersionDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 应用KEY
     */
    @TableField("app_key")
    private String appKey;

    /**
     * 命名空间ID
     */
    @TableField("namespace_id")
    private String namespaceId;

    /**
     * 命名空间正式版本号
     */
    @TableField("namespace_version")
    private String namespaceVersion;

    /**
     * 命名空间变更版本号
     */
    @TableField("namespace_change_version")
    private String namespaceChangeVersion;

    /**
     * 变更类型
     */
    @TableField("change_type")
    private NamespaceVersionChangeType changeType;

    /**
     * 发起变更的发布单版本号
     */
    @TableField("release_version")
    private String releaseVersion;

    /**
     * 是否最新版本
     */
    @TableField("is_available")
    private Available isAvailable;

    /**
     * 第一次应用该变更的索引版本号
     */
    @TableField("index_version")
    private String indexVersion;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified", fill = FieldFill.INSERT_UPDATE)
    private Date gmtModified;

    /**
     * 创建者
     */
    @TableField(value = "creator", fill = FieldFill.INSERT)
    private String creator;

    /**
     * 修改者
     */
    @TableField(value = "modifier", fill = FieldFill.INSERT_UPDATE)
    private String modifier;
}
