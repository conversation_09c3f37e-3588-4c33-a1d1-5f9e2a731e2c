package com.taobao.wireless.orange.dal.enhanced.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.taobao.wireless.orange.dal.enhanced.entity.OConditionDO;

import java.util.List;

/**
 * <p>
 * 条件表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
public interface OConditionDAO extends IService<OConditionDO> {

    public List<OConditionDO> getByNames(String namespaceId, List<String> conditionNames);

    public OConditionDO getByConditionId(String conditionId);
}
