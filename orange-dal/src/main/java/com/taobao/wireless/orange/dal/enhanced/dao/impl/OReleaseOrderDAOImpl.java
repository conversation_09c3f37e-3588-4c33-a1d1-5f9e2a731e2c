package com.taobao.wireless.orange.dal.enhanced.dao.impl;

import com.taobao.wireless.orange.dal.enhanced.entity.OReleaseOrderDO;
import com.taobao.wireless.orange.dal.enhanced.mapper.OReleaseOrderMapper;
import com.taobao.wireless.orange.dal.enhanced.dao.OReleaseOrderDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 发布单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Service
public class OReleaseOrderDAOImpl extends ServiceImpl<OReleaseOrderMapper, OReleaseOrderDO> implements OReleaseOrderDAO {

    @Override
    public OReleaseOrderDO getByReleaseVersion(String releaseVersion) {
        return lambdaQuery().eq(OReleaseOrderDO::getReleaseVersion, releaseVersion).one();
    }
}
