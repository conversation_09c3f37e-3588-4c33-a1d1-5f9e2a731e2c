package com.taobao.wireless.orange.dal.enhanced.dao.impl;

import com.taobao.wireless.orange.dal.enhanced.entity.ONamespaceVersionDO;
import com.taobao.wireless.orange.dal.enhanced.mapper.ONamespaceVersionMapper;
import com.taobao.wireless.orange.dal.enhanced.dao.ONamespaceVersionDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 命名空间版本表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
@Service
public class ONamespaceVersionDAOImpl extends ServiceImpl<ONamespaceVersionMapper, ONamespaceVersionDO> implements ONamespaceVersionDAO {

}
