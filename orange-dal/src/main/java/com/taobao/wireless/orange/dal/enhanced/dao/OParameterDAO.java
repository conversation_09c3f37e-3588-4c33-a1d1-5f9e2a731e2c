package com.taobao.wireless.orange.dal.enhanced.dao;

import com.taobao.wireless.orange.dal.enhanced.entity.OParameterDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 参数表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
public interface OParameterDAO extends IService<OParameterDO> {

    public OParameterDO getByParameterId(String parameterId);

    public boolean saveOrUpdateBatchByParameterId(List<OParameterDO> parameters);
}
