package com.taobao.wireless.orange.service.model;

import com.taobao.wireless.orange.common.annotation.EnumValidation;
import com.taobao.wireless.orange.common.constant.enums.ChangeType;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class ConditionChangeDTO {
    /**
     * 条件id
     */
    @NotBlank(message = "条件id不能为空")
    private String conditionId;

    /**
     * 条件表达式
     */
    private ConditionExpressionDTO expression;

    /**
     * 修改前发布版本号
     */
    private String previousReleaseVersion;

    /**
     * 变更类型
     */
    @EnumValidation(clazz = ChangeType.class, method = "getCode", message = "变更类型不合法")
    private ChangeType changeType;

    /**
     * 条件名称
     */
    @NotBlank(message = "条件名称不能为空")
    private String name;

    /**
     * 条件颜色
     */
    private String color;
}
