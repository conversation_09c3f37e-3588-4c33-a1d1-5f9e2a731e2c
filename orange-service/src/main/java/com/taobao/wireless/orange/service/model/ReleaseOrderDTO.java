package com.taobao.wireless.orange.service.model;

import com.taobao.wireless.orange.common.constant.enums.ReleaseOrderBizType;
import com.taobao.wireless.orange.common.constant.enums.ReleaseOrderStatus;
import com.taobao.wireless.orange.common.constant.enums.ReleaseType;
import lombok.Data;

import java.util.Date;

@Data
public class ReleaseOrderDTO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 发布版本号
     */
    private String releaseVersion;

    /**
     * 应用KEY
     */
    private String appKey;

    /**
     * 命名空间ID
     */
    private String namespaceId;

    /**
     * 发布对象类型
     */
    private ReleaseOrderBizType bizType;

    /**
     * 发布对象ID
     */
    private String bizId;

    /**
     * 发布类型
     */
    private ReleaseType releaseType;

    /**
     * 描述
     */
    private String description;

    /**
     * 状态
     */
    private ReleaseOrderStatus status;

    /**
     * 发布百分比（十万分之一为单位）
     */
    private Integer percent;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 修改者
     */
    private String modifier;
}
