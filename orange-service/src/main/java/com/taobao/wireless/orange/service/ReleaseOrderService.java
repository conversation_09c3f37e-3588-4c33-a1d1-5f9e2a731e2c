package com.taobao.wireless.orange.service;

import com.taobao.unifiedsession.core.json.JSON;
import com.taobao.wireless.orange.common.constant.enums.OperationType;
import com.taobao.wireless.orange.common.model.Pagination;
import com.taobao.wireless.orange.common.model.PaginationResult;
import com.taobao.wireless.orange.common.model.Result;
import com.taobao.wireless.orange.common.util.BeanUtil;
import com.taobao.wireless.orange.common.util.Pipe;
import com.taobao.wireless.orange.dal.enhanced.entity.OConditionDO;
import com.taobao.wireless.orange.dal.enhanced.entity.OReleaseOrderDO;
import com.taobao.wireless.orange.manager.ReleaseOrderManager;
import com.taobao.wireless.orange.manager.model.*;
import com.taobao.wireless.orange.manager.util.PageUtil;
import com.taobao.wireless.orange.service.model.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class ReleaseOrderService {
    @Autowired
    private ReleaseOrderManager releaseOrderManager;

    public PaginationResult<ReleaseOrderDTO> query(ReleaseOrderQueryDTO query, Pagination pagination) {
        return Pipe.of(query)
                .map(q -> BeanUtil.createFromProperties(query, OReleaseOrderDO.class))
                .map(q -> releaseOrderManager.query(q, pagination))
                .map(r -> PageUtil.convert(r, ReleaseOrderDTO.class))
                .get();
    }

    public Result<String> create(ReleaseOrderCreateDTO releaseOrder) {
        return Pipe.of(releaseOrder)
                .map(this::convert)
                .map(releaseOrderManager::create)
                .map(Result::new)
                .get();
    }

    public Result<Void> publish(String releaseVersion) {
        releaseOrderManager.publish(releaseVersion);
        return Result.success();
    }

    public Result<Void> cancel(String releaseVersion) {
        releaseOrderManager.cancel(releaseVersion);
        return Result.success();
    }

    public Result<Void> ratioGray(String releaseVersion, RatioGrayDTO ratioGray) {
        releaseOrderManager.ratioGray(releaseVersion, ratioGray.getPercent());
        return Result.success();
    }

    public Result<Void> verify(String releaseVersion) {
        releaseOrderManager.verify(releaseVersion);
        return Result.success();
    }

    public Result<ReleaseOrderDetailDTO> getDetail(String releaseVersion) {
        return Pipe.of(releaseVersion)
                .map(releaseOrderManager::getDetail)
                .map(this::convert)
                .map(Result::new)
                .get();
    }

    public Result<List<ReleaseOrderChangesDTO>> getChanges(String releaseVersion) {
        return Pipe.of(releaseVersion)
                .map(releaseOrderManager::getChanges)
                .map(this::convert)
                .map(Result::new)
                .get();
    }

    public Result<List<ReleaseOrderOperationDTO>> getOperations(String releaseVersion, List<OperationType> operationTypes) {
        return Pipe.of(releaseOrderManager.getOperations(releaseVersion, operationTypes))
                .map(operations -> {
                    return BeanUtil.createFromProperties(operations, ReleaseOrderOperationDTO.class);
                })
                .map(Result::new)
                .get();
    }

    private List<ReleaseOrderChangesDTO> convert(List<ParameterChangeBO> parameterChanges) {
        return parameterChanges.stream().map(change -> {
            ReleaseOrderChangesDTO releaseOrderChangesDTO = new ReleaseOrderChangesDTO();

            ParameterChangeDTO parameterChange = BeanUtil.createFromProperties(change.getChange(), ParameterChangeDTO.class);
            parameterChange.setParameterConditionChanges(BeanUtil.createFromProperties(change.getChange().getParameterConditionVersionBOS(), ParameterConditionChangeDTO.class));
            releaseOrderChangesDTO.setParameterChange(parameterChange);

            if (change.getBefore() != null) {
                ParameterDetailDTO parameterReleasedDetail = BeanUtil.createFromProperties(change.getBefore(), ParameterDetailDTO.class);
                parameterReleasedDetail.setParameterConditions(BeanUtil.createFromProperties(change.getBefore().getParameterConditionVersionBOS(), ParameterConditionDTO.class));
                releaseOrderChangesDTO.setParameterReleasedDetail(parameterReleasedDetail);
            }

            return releaseOrderChangesDTO;
        }).collect(Collectors.toList());
    }

    private ReleaseOrderBO convert(ReleaseOrderCreateDTO releaseOrder) {
        ReleaseOrderBO releaseOrderBO = BeanUtil.createFromProperties(releaseOrder, ReleaseOrderBO.class);

        List<ParameterChangeDTO> parameterChanges = releaseOrder.getParameterChanges();
        if (CollectionUtils.isNotEmpty(parameterChanges)) {
            List<ParameterVersionBO> parameterVersionBOS = parameterChanges.stream().map(parameterChangeDTO -> {
                ParameterVersionBO parameterVersionBO = BeanUtil.createFromProperties(parameterChangeDTO, ParameterVersionBO.class);
                parameterVersionBO.setParameterBO(BeanUtil.createFromProperties(parameterChangeDTO, ParameterBO.class));
                parameterVersionBO.setConditionNamesOrder(parameterChangeDTO.getConditionNamesOrder());

                List<ParameterConditionChangeDTO> parameterConditionChanges = parameterChangeDTO.getParameterConditionChanges();
                if (CollectionUtils.isNotEmpty(parameterConditionChanges)) {
                    List<ParameterConditionVersionBO> parameterConditionVersionBOS = BeanUtil.createFromProperties(parameterConditionChanges, ParameterConditionVersionBO.class);
                    parameterVersionBO.setParameterConditionVersionBOS(parameterConditionVersionBOS);
                }

                return parameterVersionBO;
            }).collect(Collectors.toList());
            releaseOrderBO.setParameterVersionBOS(parameterVersionBOS);
        }

        List<ConditionChangeDTO> conditionChanges = releaseOrder.getConditionChanges();
        List<ConditionVersionBO> conditionVersionBOS = CollectionUtils.isNotEmpty(conditionChanges) ? conditionChanges.stream().map(conditionChangeDTO -> {
            ConditionVersionBO conditionVersionBO = BeanUtil.createFromProperties(conditionChangeDTO, ConditionVersionBO.class);
            conditionVersionBO.setCondition(BeanUtil.createFromProperties(conditionChangeDTO, OConditionDO.class));
            conditionVersionBO.setExpression(JSON.toJSONString(conditionChangeDTO.getExpression()));
            return conditionVersionBO;
        }).collect(Collectors.toList()) : new ArrayList<>();
        releaseOrderBO.setConditionVersionBOS(conditionVersionBOS);

        return releaseOrderBO;
    }

    private ReleaseOrderDetailDTO convert(ReleaseOrderBO releaseOrder) {
        ReleaseOrderDetailDTO releaseOrderDetailDTO = BeanUtil.createFromProperties(releaseOrder, ReleaseOrderDetailDTO.class);
        if (CollectionUtils.isNotEmpty(releaseOrder.getParameterVersionBOS())) {
            releaseOrderDetailDTO.setParameterKeys(releaseOrder.getParameterVersionBOS().stream().map(ParameterVersionBO::getParameterKey).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(releaseOrder.getConditionVersionBOS())) {
            releaseOrderDetailDTO.setConditionIds(releaseOrder.getConditionVersionBOS().stream().map(ConditionVersionBO::getConditionId).collect(Collectors.toList()));
        }
        return releaseOrderDetailDTO;
    }

//    List<ReleaseChangeBO> getChanges(Long id);
//
//    List<ReleaseO> getOperations(Long id);
}
