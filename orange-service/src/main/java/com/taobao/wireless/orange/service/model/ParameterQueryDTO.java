package com.taobao.wireless.orange.service.model;

import com.taobao.wireless.orange.common.annotation.EnumValidation;
import com.taobao.wireless.orange.common.constant.enums.ParameterStatus;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class ParameterQueryDTO {
    @NotBlank(message = "命名空间ID不能为空")
    private String namespaceId;

    private String parameterKey;

    /**
     * 搜索关键字
     */
    private String keyword;

    @EnumValidation(clazz = ParameterStatus.class, method = "getCode", message = "状态不合法")
    private ParameterStatus status;
}
