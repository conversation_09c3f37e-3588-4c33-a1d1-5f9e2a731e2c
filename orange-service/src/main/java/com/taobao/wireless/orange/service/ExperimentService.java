package com.taobao.wireless.orange.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taobao.wireless.orange.manager.model.ExperimentBO;
import com.taobao.wireless.orange.manager.model.ReleaseOrderBO;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ExperimentService {

    Page<ExperimentBO> query(ExperimentBO query) {
        return null;
    }

    Long create(ExperimentBO experiment) {
        return null;
    }

    Boolean update(ExperimentBO experiment) {
        return null;
    }


    Boolean start(Long id) {
        return null;
    }

    Boolean stop(Long id) {
        return null;
    }

    List<ReleaseOrderBO> listVersions(Long id) {
        return null;
    }
}
