package com.taobao.wireless.orange.service.model;

import com.taobao.wireless.orange.common.constant.enums.NamespaceBizType;
import com.taobao.wireless.orange.common.constant.enums.NamespaceStatus;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 命名空间传输对象
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-15
 */
@Data
public class NamespaceDTO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 命名空间ID
     */
    private String namespaceId;

    /**
     * 应用KEY
     */
    private String appKey;

    /**
     * 命名空间的类型
     */
    private NamespaceBizType bizType;

    /**
     * 命名空间类型对应的实体ID
     */
    private String bizId;

    /**
     * 命名空间类型对应的实体名称
     */
    private String bizName;

    /**
     * 负责人列表
     */
    private List<String> owners;

    /**
     * 测试人员列表
     */
    private List<String> testers;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 修改者
     */
    private String modifier;

    /**
     * 状态
     */
    private NamespaceStatus status;

    /**
     * 名称
     */
    private String name;

    /**
     * 描述
     */
    private String description;
}
