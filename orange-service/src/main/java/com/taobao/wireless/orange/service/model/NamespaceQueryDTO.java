package com.taobao.wireless.orange.service.model;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class NamespaceQueryDTO {
    /**
     * 应用 KEY
     */
    @NotBlank(message = "应用KEY不能为空")
    private String appKey;
    /**
     * 命名空间名称
     */
    private String name;
    /**
     * 命名空间状态
     */
    private String status;
    /**
     * 搜索关键字
     */
    private String keyword;
    /**
     * 是否有权限
     */
    private Boolean hasPermission;
}
