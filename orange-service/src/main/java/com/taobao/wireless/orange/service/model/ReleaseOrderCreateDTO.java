package com.taobao.wireless.orange.service.model;

import com.taobao.wireless.orange.common.annotation.EnumValidation;
import com.taobao.wireless.orange.common.constant.enums.ReleaseOrderBizType;
import com.taobao.wireless.orange.common.constant.enums.ReleaseType;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.List;

@Data
public class ReleaseOrderCreateDTO {
    /**
     * 命名空间ID
     */
    @NotBlank(message = "命名空间ID不能为空")
    private String namespaceId;
    /**
     * 发布对象类型
     */
    @NotBlank(message = "发布对象类型不能为空")
    @EnumValidation(clazz = ReleaseOrderBizType.class, method = "getCode", message = "发布对象类型不合法")
    private ReleaseOrderBizType bizType;
    /**
     * 发布对象ID
     */
    private String bizId;
    /**
     * 发布类型
     */
    @NotBlank(message = "发布类型不能为空")
    @EnumValidation(clazz = ReleaseType.class, method = "getCode", message = "发布类型不合法")
    private ReleaseType releaseType;
    /**
     * 描述
     */
    private String description;

    /**
     * 本次发布单涉及的参数变更
     */
    @Valid
    private List<ParameterChangeDTO> parameterChanges;

    /**
     * 本次发布单涉及的条件变更
     */
    @Valid
    private List<ConditionChangeDTO> conditionChanges;
}
