package com.taobao.wireless.orange.common.exception;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class CommonException extends RuntimeException {

    /**
     * 响应码中的占位符
     */
    public static final String FORMAT = "\\{\\}";

    /**
     * 业务错误码
     */
    private String code;
    /**
     * 业务错误信息
     */
    private String msg;

    public CommonException(ExceptionEnum code) {
        super(code.getMessage());
        this.code = code.getCode();
        this.msg = code.getMessage();
    }

    public CommonException(ExceptionEnum code, String message) {
        super(message);
        this.code = code.getCode();
        this.msg = message;
    }

    public CommonException(ExceptionEnum code, String message, Throwable cause) {
        // 不将 message 传给父类，防止覆盖 cause 的 message
        super(cause);
        this.code = code.getCode();
        this.msg = message;
    }

    public CommonException(ExceptionEnum code, Throwable cause) {
        super(cause);
        this.code = code.getCode();
        this.msg = code.getMessage();
    }

    public static CommonException getDynamicException(ExceptionEnum code, Object... objs) {
        String msg = CommonException.replace(code.getMessage(), objs);
        return new CommonException(code, msg);
    }

    /**
     * 替换响应码消息中的占位符{}
     *
     * @param msg  响应码消息
     * @param objs 替换参数
     * @return 替换后的响应码消息
     */
    private static String replace(String msg, Object... objs) {
        if (objs == null) {
            return msg;
        }
        for (Object obj : objs) {
            // 兼容部分三方接口返回的 message 为 null 的场景
            msg = msg.replaceFirst(FORMAT, obj == null ? "" : obj.toString());
        }
        return msg;
    }
}
