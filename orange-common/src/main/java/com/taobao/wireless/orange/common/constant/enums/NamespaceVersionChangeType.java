package com.taobao.wireless.orange.common.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum NamespaceVersionChangeType {
    NEW_RELEASE("NEW_RELEASE"),
    RATIO_GRAY("RATIO_GRAY"),
    FINISH_RELEASE("FINISH_RELEASE"),
    CANCEL_RELEASE("CANCEL_RELEASE"),
    DELETE_NAMESPACE("DELETE_NAMESPACE"),
    ;

    private final String code;
}
