package com.taobao.wireless.orange.common.filter;

import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.common.model.BaseResult;
import com.taobao.wireless.orange.common.model.PaginationResult;
import com.taobao.wireless.orange.common.model.Result;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 统一异常结果转换类
 *
 * <AUTHOR>
 */
@Aspect
@Order(10)
@Component
@Slf4j
public class ExceptionFilter {
    @Around("execution(com.taobao.wireless.orange.common.model.Result com.taobao.wireless.orange..*.*(..))")
    public Result filterResultException(ProceedingJoinPoint proceedingJoinPoint) {
        return (Result) this.formatResult(new Result(), proceedingJoinPoint);
    }

    @Around("execution(com.taobao.wireless.orange.common.model.PaginationResult com.taobao.wireless.orange..*.*(..))")
    public PaginationResult filterPaginationResultException(ProceedingJoinPoint proceedingJoinPoint) {
        return (PaginationResult) this.formatResult(new PaginationResult(), proceedingJoinPoint);
    }

    private BaseResult formatResult(BaseResult baseResult, ProceedingJoinPoint proceedingJoinPoint) {
        baseResult.setSuccess(false);
        try {
            baseResult = (BaseResult) proceedingJoinPoint.proceed();
        } catch (CommonException e) {
            // 业务异常
            log.error("System Common Exception: {}", e.getMessage(), e);
            baseResult.setCode(e.getCode());
            baseResult.setMessage(e.getMessage());
        } catch (Throwable e) {
            // 系统异常
            log.error("System Throwable Exception: {}", e.getMessage(), e);
            baseResult.setCode(ExceptionEnum.SYSTEM_EXCEPTION.getCode());
            baseResult.setMessage(ExceptionEnum.SYSTEM_EXCEPTION.getMessage() + ": " + e.getMessage());
        }
        return baseResult;
    }
}
