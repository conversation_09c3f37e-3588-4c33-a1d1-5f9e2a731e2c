package com.taobao.wireless.orange.common.model;

import com.taobao.wireless.orange.common.util.UserUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class User {
    /***
     * bucId
     */
    private Integer id;

    /**
     * 工号
     */
    private String empId;
    /**
     * 中文姓名
     */
    private String name;
    /**
     * 邮箱
     */
    private String emailAddr;
    /**
     * 花名
     */
    private String nickNameCn;
    /**
     * 域账号
     */
    private String emailPrefix;

    /**
     * 主管工号
     */
    private String supervisorEmpId;

    /**
     * 离职状态 A：在职 I：离职
     */
    private String hrStatus;

    public String getDisplayName() {
        String operator = this.getNickNameCn();
        if (StringUtils.isBlank(operator)) {
            operator = this.getEmpId() + "-" + this.getName();
        }
        return operator;
    }

    public String getEmpId(String empId) {
        return UserUtil.formatWorkerId(empId);
    }
}
