package com.taobao.wireless.orange.common.util;

import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;

import java.beans.BeanInfo;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Constructor;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.lang.reflect.TypeVariable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 类拷贝
 */
public class BeanUtil {
    /**
     * 基本类型到包装类型
     **/
    private static final Map<Class<?>, Class<?>> PRIMITIVE_TYPE_TO_WRAPPER_MAP = new IdentityHashMap<Class<?>, Class<?>>(8);

    /**
     * 包装类型到基本类型
     **/
    private static final Map<Class<?>, Class<?>> PRIMITIVE_WRAPPER_TYPE_MAP = new IdentityHashMap<Class<?>, Class<?>>(8);

    static {
        /**八大基本类型**/
        PRIMITIVE_TYPE_TO_WRAPPER_MAP.put(int.class, Integer.class);
        PRIMITIVE_TYPE_TO_WRAPPER_MAP.put(boolean.class, Boolean.class);
        PRIMITIVE_TYPE_TO_WRAPPER_MAP.put(char.class, Character.class);
        PRIMITIVE_TYPE_TO_WRAPPER_MAP.put(byte.class, Byte.class);
        PRIMITIVE_TYPE_TO_WRAPPER_MAP.put(long.class, Long.class);
        PRIMITIVE_TYPE_TO_WRAPPER_MAP.put(float.class, Float.class);
        PRIMITIVE_TYPE_TO_WRAPPER_MAP.put(double.class, Double.class);
        PRIMITIVE_TYPE_TO_WRAPPER_MAP.put(short.class, Short.class);
        /**八大基本类型**/
        PRIMITIVE_WRAPPER_TYPE_MAP.put(Integer.class, int.class);
        PRIMITIVE_WRAPPER_TYPE_MAP.put(Boolean.class, boolean.class);
        PRIMITIVE_WRAPPER_TYPE_MAP.put(Character.class, char.class);
        PRIMITIVE_WRAPPER_TYPE_MAP.put(Byte.class, byte.class);
        PRIMITIVE_WRAPPER_TYPE_MAP.put(Long.class, long.class);
        PRIMITIVE_WRAPPER_TYPE_MAP.put(Float.class, float.class);
        PRIMITIVE_WRAPPER_TYPE_MAP.put(Double.class, double.class);
        PRIMITIVE_WRAPPER_TYPE_MAP.put(Short.class, short.class);

    }

    private static void copyProperties(Object source, Object target, Map<String, Class> classMap,
                                       Class<?> editable, String... ignoreProperties) throws Exception {
        copyProperties(source, target, classMap, editable, false, ignoreProperties);
    }

    private static void copyProperties(Object source, Object target, Map<String, Class> classMap,
                                       Class<?> editable, Boolean skipNullValue, String... ignoreProperties) throws Exception {

        Class<?> actualEditable = target.getClass();
        if (editable != null) {
            if (!editable.isInstance(target)) {
                throw new CommonException(ExceptionEnum.BEAN_COPY_EXCEPTION, "无法拷贝类" + editable.getName() + ":到对象" + target.getClass().getName());
            }
            actualEditable = editable;
        }
        // 通过内省拿到目标对象和源对象的属性描述
        BeanInfo targetBI = Introspector.getBeanInfo(actualEditable);
        PropertyDescriptor[] targetPds = targetBI.getPropertyDescriptors();
        BeanInfo sourceBI = Introspector.getBeanInfo(source.getClass());
        Map<String, PropertyDescriptor> sourceMap = Arrays.asList(sourceBI.getPropertyDescriptors()).stream().collect(Collectors.toMap(PropertyDescriptor::getName, p -> p, (k1, k2) -> k1));
        List<String> ignoreList = (ignoreProperties != null ? Arrays.asList(ignoreProperties) : null);

        // 遍历目标对象属性
        for (PropertyDescriptor targetPd : targetPds) {
            Method writeMethod = targetPd.getWriteMethod();
            // 判断目标类的set方法存在并且不再忽略字段中
            if (writeMethod != null && (ignoreList == null || !ignoreList.contains(targetPd.getName()))) {
                PropertyDescriptor sourcePd = sourceMap.get(targetPd.getName());
                if (sourcePd != null) {
                    Method sourceRM = sourcePd.getReadMethod();
                    // 判断属性是否可以转换
                    if (sourceRM != null && isAssignable(writeMethod.getParameterTypes()[0], sourceRM.getReturnType())) {
                        Object value = copyUtil(source, sourcePd, writeMethod, sourceRM, classMap);
                        if (skipNullValue && value == null) {
                            continue;
                        }
                        // 兼容历史一些 bool 参数被写成了 int
                        value = boolIntegerConvert(value, targetPd.getPropertyType());
                        // 兼容历史一些 bool 参数被写成了 string
                        value = boolStringConvert(value, targetPd.getPropertyType());
                        // 兼容底层 Byte，对外是 Boolean 或者 Integer
                        value = byteConvert(value, targetPd.getPropertyType());
                        // 基础类型赋值 null 会有问题
                        if (!targetPd.getPropertyType().isPrimitive() || value != null) {
                            writeMethod.invoke(target, value);
                        }
                    }
                }
            }
        }
    }

    /**
     * @param source   源对象
     * @param sourcePd 源对象的属性描述
     * @param targetWM 目标set方法
     * @param classMap 指定List深入copy map key为对象属性名, value为泛型class
     * @param sourceRM 源读取方法
     * @return
     * @throws Exception
     */
    private static Object copyUtil(Object source, PropertyDescriptor sourcePd,
                                   Method targetWM, Method sourceRM, Map<String, Class> classMap) throws Exception {
        // 判断源get方法是否是公共的
        if (!Modifier.isPublic(sourceRM.getDeclaringClass().getModifiers())) {
            sourceRM.setAccessible(true);
        }
        Object value = sourceRM.invoke(source);

        // 判断目标set方法是否是公共的
        if (!Modifier.isPublic(targetWM.getDeclaringClass().getModifiers())) {
            targetWM.setAccessible(true);
        }

        // 判断属性是否是集合类型,并且指定需要深入拷贝
        if (value != null && value instanceof Collection && classMap != null && classMap.get(sourcePd.getName()) != null) {
            Class targetClass = classMap.get(sourcePd.getName());
            value = copyList((List) value, targetClass, classMap);
        }
        return value;
    }

    /**
     * @param lhsList       源List
     * @param rhsListObject 对象 class
     * @param classMap      指定List深入copy map key为对象属性名, value为泛型class
     * @return
     * @throws Exception
     */
    private static List<Object> copyList(List<Object> lhsList, Class rhsListObject, Map<String, Class> classMap) throws Exception {
        List<Object> rhsList = new ArrayList<>();

        Constructor[] constructors = rhsListObject.getConstructors();
        for (Iterator iter = lhsList.iterator(); iter.hasNext(); ) {
            Object source = iter.next();
            /**不存在公共构造器,说明此类不能实例化*/
            if (constructors.length != 0) {
                //获取到第一个构造器(无参构造器)
                TypeVariable[] typeVariables = constructors[0].getTypeParameters();
                //如果存在无参构造器直接创建并且拷贝 否则不处理
                if (typeVariables.length == 0) {
                    Object tagetObj = constructors[0].newInstance();
                    copyProperties(source, tagetObj, classMap, rhsListObject);
                    rhsList.add(tagetObj);
                }
            } else {
                rhsList.add(source);
            }
        }
        return rhsList;
    }

    /**
     * 判断属性是否可以转换  包括基本类型以及List 等等等等
     *
     * @param lhsType 左边的
     * @param rhsType 右边的
     * @return
     */
    private static boolean isAssignable(Class<?> lhsType, Class<?> rhsType) {
        if (lhsType.isAssignableFrom(rhsType)) {
            return true;
        }
        Class<?> resolvedPrimitive = PRIMITIVE_WRAPPER_TYPE_MAP.get(rhsType);
        Class<?> resolvedWrapper = PRIMITIVE_TYPE_TO_WRAPPER_MAP.get(rhsType);
        if (lhsType.isPrimitive() && lhsType == resolvedPrimitive) {
            return true;
        }
        if (resolvedWrapper != null && lhsType.isAssignableFrom(resolvedWrapper)) {
            return true;
        }
        boolean integer2Boolean = (Integer.TYPE.equals(lhsType) || Integer.class.equals(lhsType)) && Boolean.class.equals(rhsType);
        boolean boolean2Integer = Boolean.class.equals(lhsType) && (Integer.TYPE.equals(rhsType) || Integer.class.equals(rhsType));
        if (integer2Boolean || boolean2Integer) {
            return true;
        }
        if (Boolean.class.equals(lhsType) && String.class.equals(rhsType)) {
            return true;
        }
        if (String.class.equals(lhsType) && Boolean.class.equals(rhsType)) {
            return true;
        }
        if (Integer.class.equals(lhsType) && Byte.class.equals(rhsType)) {
            return true;
        }
        if (Boolean.class.equals(lhsType) && Byte.class.equals(rhsType)) {
            return true;
        }
        return false;
    }

    ////////////////////////////对外工具/////////////////////

    /**
     * 嵌套list
     *
     * @param sourceList 源数据 (不能为空)
     * @param targetList 目标对象 (可以为空,实现可以自己创建对象并且返回)
     * @param targetType 目标对象的class (指定的字段会进行深入解析copy不指定拷贝引用)
     * @param classMap   指定的字段会进行深入解析copy不指定拷贝引用
     * @param <S>
     * @param <T>        此方法已经过时 请使用listCopyProperties 方法
     */
    @Deprecated
    private static <S, T> List<T> copyProperties(List<S> sourceList, List<T> targetList, Class<T> targetType, Map<String, Class> classMap) {
        if (sourceList == null || targetList == null) {
            return null;
        }
        for (S source : sourceList) {
            T target = null;
            try {
                if (targetList == null) {
                    targetList = new ArrayList<>();
                }
                target = targetType.getConstructor().newInstance();
                copyProperties(source, target, classMap);
                targetList.add(target);
            } catch (Exception e) {
                throw new CommonException(ExceptionEnum.BEAN_COPY_EXCEPTION, e);
            }
        }
        return targetList;
    }

    /**
     * @param source     源数据
     * @param target     拷贝对象数据
     * @param containMap 特别说明,  class map中是存放list中元素中的泛型对象实例中的某个字段的名字和class类别
     *                   如 左边元素中对象(source)有个属性叫做 List<AO> aos  ,  右边元素对象(target)属性是List<BO> aos, 此时我要将左边拷贝到右边  时候需要额外传递一个参数
     *                   就是classMap  classMap.put("aos",BO.class);
     *                   <p>
     *                   此方法已经过时 建议使用copyProperties(Object source, Class<T> targetClass, Map<String, Class> containMap) 方法
     */
    public static void copyProperties(Object source, Object target, Map<String, Class> containMap) {
        if (source == null || target == null) {
            return;
        }
        try {
            copyProperties(source, target, containMap, null, null);
        } catch (Exception e) {
            throw new CommonException(ExceptionEnum.BEAN_COPY_EXCEPTION, e);
        }
    }

    /**
     * @param source
     * @param target
     */
    public static void copyProperties(Object source, Object target) {
        if (source == null || target == null) {
            return;
        }
        try {
            copyProperties(source, target, null, null, true, null);
        } catch (Exception e) {
            throw new CommonException(ExceptionEnum.BEAN_COPY_EXCEPTION, e);
        }
    }

    /**
     * 将源列表数据转换为指定类型的目标列表数据
     *
     * @param source     源列表数据
     * @param targetType 目标列表数据类型
     * @return 将源数据转换为指定类型的目标数据
     */
    public static <S, T> List<T> createFromProperties(List<S> source, Class<T> targetType) {
        return createFromProperties(source, targetType, null);
    }

    /**
     * 将源数据转换为指定类型的目标数据
     *
     * @param source     源数据
     * @param targetType 目标数据类型
     * @return 将源数据转换为指定类型的目标数据
     */
    public static <S, T> T createFromProperties(S source, Class<T> targetType) {
        return createFromProperties(source, targetType, null);
    }

    /**
     * 将源列表数据转换为指定类型的目标列表数据
     *
     * @param source     源列表数据
     * @param targetType 目标数据类型
     * @param classMap   指定List深入copy map key为对象属性名, value为泛型class
     * @return 将源列表数据转换为指定类型的目标列表数据
     */
    public static <S, T> List<T> createFromProperties(List<S> source, Class<T> targetType, Map<String, Class> classMap) {
        List<T> target = new ArrayList<>();
        copyProperties(source, target, targetType, classMap);
        return target;
    }

    /**
     * 将源数据转换为指定类型的目标数据
     *
     * @param source     源数据
     * @param targetType 目标数据类型
     * @param classMap   指定List深入copy map key为对象属性名, value为泛型class
     * @return 将源数据转换为指定类型的目标数据
     */
    public static <S, T> T createFromProperties(S source, Class<T> targetType, Map<String, Class> classMap) {
        if (null == source || null == targetType) {
            return null;
        }
        try {
            T target = targetType.getConstructor().newInstance();
            copyProperties(source, target, classMap);
            return target;
        } catch (Exception e) {
            throw new CommonException(ExceptionEnum.BEAN_COPY_EXCEPTION, e);
        }
    }

    private static Object boolStringConvert(Object value, Class tClass) {
        if (value == null) {
            return null;
        } else if (value.getClass() == tClass) {
            return value;
        } else if (value.getClass() == Boolean.class && tClass == String.class) {
            return (Boolean) value ? "true" : "false";
        } else if (value.getClass() == String.class && tClass == Boolean.class) {
            return "true".equals(value);
        } else {
            return value;
        }
    }

    private static Object byteConvert(Object value, Class tClass) {
        if (value == null) {
            return null;
        } else if (value.getClass() == tClass) {
            return value;
        } else if (value.getClass() == Byte.class && tClass == Integer.class) {
            return Byte.toUnsignedInt((Byte) value);
        } else if (value.getClass() == Byte.class && tClass == Boolean.class) {
            return Objects.equals((byte) 1, value);
        } else {
            return value;
        }
    }

    private static Object boolIntegerConvert(Object value, Class tClass) {
        if (value == null) {
            return null;
        }
        if (value.getClass() == tClass) {
            return value;
        }
        boolean boolean2Integer = value.getClass() == Boolean.class && (tClass == Integer.TYPE || tClass == Integer.class);
        if (boolean2Integer) {
            return (Boolean) value ? 1 : 0;
        }
        boolean integer2Boolean = (value.getClass() == Integer.TYPE || value.getClass() == Integer.class) && tClass == Boolean.class;
        if (integer2Boolean) {
            return value.equals(1);
        }
        return value;
    }
}

