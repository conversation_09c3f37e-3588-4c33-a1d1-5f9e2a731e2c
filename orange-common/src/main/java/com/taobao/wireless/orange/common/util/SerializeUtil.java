package com.taobao.wireless.orange.common.util;

import java.util.UUID;

public class SerializeUtil {
    private static final Snowflake snowflake = new Snowflake();

    /**
     * 生成 ID
     *
     * @return
     */
    public static String UUID() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 生成递增的版本号
     */
    public static long version() {
        return snowflake.nextId();
    }
}
