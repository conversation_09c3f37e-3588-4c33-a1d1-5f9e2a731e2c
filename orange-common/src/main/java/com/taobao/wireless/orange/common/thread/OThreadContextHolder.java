package com.taobao.wireless.orange.common.thread;

public class OThreadContextHolder {

    private static final InheritableThreadLocal<OThreadContext> threadLocal = new InheritableThreadLocal<>();

    public static OThreadContext get() {
        return threadLocal.get();
    }

    public static void clear() {
        threadLocal.remove();
    }

    public static void set(OThreadContext threadContext) {
        threadLocal.set(threadContext);
    }
}
