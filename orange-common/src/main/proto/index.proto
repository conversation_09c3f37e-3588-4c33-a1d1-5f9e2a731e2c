syntax = "proto3";

package com.taobao.wireless.orange.common.model;

import "common.proto";

option java_package = "com.taobao.wireless.orange.common.model.proto";
option java_outer_classname = "IndexOuterClass";
option java_multiple_files = true;

message IndexProto {
  // 索引协议版本，用于指导如何解析和消费配置文件
  string schema_version = 1;

  // CDN 域名，用于资源下载
  string cdn = 2;

  // 应用标识
  string app_key = 3;

  // 索引版本号
  int64 version = 4;

  // 基础版本号，"0"代表全量索引，其他代表差量索引
  int64 base_version = 5;

  // 需要下线的命名空间(仅适用于差量索引会有)
  repeated string offline_namespaces = 6;

  // 配置发布策略，FULL/INCREMENTAL
  ConfigStrategyProto strategy = 7;

  // 命名空间数组
  repeated NamespaceProto namespaces = 8;
}

message NamespaceProto {
  // 命名空间名称
  string name = 1;

  // namespace 变更版本，如果版本大于本地版本则需要触发百分比计算和实验下载
  int64 change_version = 2;

  // 正式配置资源的版本号
  int64 version = 3;

  // 正式发布配置，包括资源地址和 MD5 值
  ConfigProto release = 4;

  // 实验配置，包括资源地址和 MD5 值
  ConfigProto experiment = 5;

  // 灰度发布配置，包括资源地址和 MD5 值及发布单
  ConfigWithOrdersProto gray = 6;
}

message ConfigProto {
  // 资源地址
  string resource_id = 1;

  // 资源内容的 MD5 值，用于内容变化判断
  string resource_md5 = 2;
}

message ConfigWithOrdersProto {
  // 发布单数组，包含该 namespace 当前所有进行中的发布单
  repeated OrderProto orders = 1;

  // 资源地址
  string resource_id = 2;

  // 资源内容的 MD5 值，用于内容变化判断
  string resource_md5 = 3;
}

message OrderProto {
  // 发布单版本，用于灰度打散设备
  int64 version = 1;

  // 灰度百分比，用于计算是否命中灰度，以十万分之一为单位
  int32 percent = 2;
}
