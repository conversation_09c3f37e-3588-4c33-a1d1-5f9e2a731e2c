syntax = "proto3";

package com.taobao.wireless.orange.common.model;

option java_package = "com.taobao.wireless.orange.common.model.proto";
option java_outer_classname = "CommonOuterClass";
option java_multiple_files = true;

enum ConfigStrategyProto {
  FULL = 0;
  INCREMENTAL = 1;
}

enum ConfigTypeProto {
  RELEASE = 0;
  EXPERIMENT = 1;
  GRAY = 2;
}

enum ParameterValueTypeProto {
  STRING = 0;
  BOOLEAN = 1;
  JSON = 2;
}

message ConditionProto {
  string id = 1;

  // 条件表达式
  ExpressionProto expression = 2;
}

// 条件表达式
message ExpressionProto {
  // 逻辑运算符
  string operator = 1;

  // 子表达式列表
  repeated ExpressionProto children = 2;

  // 表达式的键
  string key = 3;

  // 表达式的值
  string value = 4;
}

message ParameterProto {
  // 参数 KEY，参数唯一标志
  string key = 1;

  // 参数版本号
  int64 version = 2;

  // 参数值类型
  ParameterValueTypeProto value_type = 3;

  // 条件值数组，根据条件应用不同的配置
  repeated ConditionalValueProto conditional_values = 4;

  // 参数值，目前仅支持 bool 和 string 类型
  oneof default_value {
    bool default_bool_value = 5;
    string default_string_value = 6;
  }
}

message ConditionalValueProto {
  string condition_id = 1;

  // 参数值，目前仅支持 bool 和 string 类型
  oneof value {
    bool bool_value = 2;
    string string_value = 3;
  }
}
