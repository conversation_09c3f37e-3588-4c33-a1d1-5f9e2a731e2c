package com.taobao.wireless.orange.manager.model;

import lombok.Data;

import java.util.List;

@Data
public class Expression {
    /**
     * 逻辑运算符，指定表达式的运算类型
     * 示例值: "AND", ">", "="
     */
    private String operator;

    /**
     * 子表达式列表，用于组成复杂的条件逻辑
     */
    private List<Expression> children;

    /**
     * 表达式的键，用于匹配环境变量或上下文
     * 示例值: "appVersion"
     */
    private String key;

    /**
     * 表达式的值，与键一起确定条件是否满足
     * 示例值: "1.0.1"
     */
    private String value;
}
