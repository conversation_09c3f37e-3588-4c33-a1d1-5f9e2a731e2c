package com.taobao.wireless.orange.manager;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taobao.wireless.orange.common.constant.enums.ChangeType;
import com.taobao.wireless.orange.common.constant.enums.ConditionStatus;
import com.taobao.wireless.orange.common.constant.enums.VersionStatus;
import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.common.model.Pagination;
import com.taobao.wireless.orange.common.util.BeanUtil;
import com.taobao.wireless.orange.common.util.SerializeUtil;
import com.taobao.wireless.orange.dal.enhanced.dao.OConditionDAO;
import com.taobao.wireless.orange.dal.enhanced.dao.OConditionVersionDAO;
import com.taobao.wireless.orange.dal.enhanced.dao.OParameterConditionVersionDAO;
import com.taobao.wireless.orange.dal.enhanced.dao.OParameterDAO;
import com.taobao.wireless.orange.dal.enhanced.entity.*;
import com.taobao.wireless.orange.manager.model.ConditionBO;
import com.taobao.wireless.orange.manager.model.ConditionVersionBO;
import com.taobao.wireless.orange.manager.model.ParameterConditionVersionBO;
import com.taobao.wireless.orange.manager.util.PageUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class ConditionManager {

    @Autowired
    private OConditionDAO conditionDAO;

    @Autowired
    private OConditionVersionDAO conditionVersionDAO;

    @Autowired
    private OParameterConditionVersionDAO parameterConditionVersionDAO;

    @Autowired
    private OParameterDAO parameterDAO;

    /**
     * 批量创建条件变更
     *
     * @param namespace 命名空间
     * @param releaseVersion    发布版本
     * @param conditionVersions 条件版本列表
     */
    public void createConditionVersions(ONamespaceDO namespace, String releaseVersion, List<ConditionVersionBO> conditionVersions) {
        if (CollectionUtils.isEmpty(conditionVersions)) {
            return;
        }

        // 条件实体
        List<OConditionDO> newConditions = conditionVersions.stream().map(conditionVersionBO -> {
            OConditionDO condition = conditionVersionBO.getCondition();

            // 说明是新增的条件
            if (StringUtils.isBlank(condition.getConditionId())) {
                String conditionId = SerializeUtil.UUID();
                // 为入参对象填充 conditionId
                conditionVersionBO.setConditionId(conditionId);
                condition.setAppKey(namespace.getAppKey());
                condition.setNamespaceId(namespace.getNamespaceId());
                condition.setConditionId(conditionId);
            }

            condition.setStatus(ConditionStatus.IN_PUBLISH);
            return condition;
        }).collect(Collectors.toList());

        List<String> conditionIds = conditionVersions.stream()
                .map(ConditionVersionBO::getConditionId)
                .collect(Collectors.toList());

        var onlineConditionId2Versions = getOnlineConditionVersions(conditionIds);

        // 创建条件版本
        List<OConditionVersionDO> newConditionVersions = conditionVersions.stream().map(conditionVersionBO -> {
            OConditionDO condition = conditionVersionBO.getCondition();

            conditionVersionBO.setStatus(VersionStatus.INIT);
            conditionVersionBO.setAppKey(namespace.getAppKey());
            conditionVersionBO.setNamespaceId(namespace.getNamespaceId());
            conditionVersionBO.setReleaseVersion(releaseVersion);
            OConditionVersionDO onlineConditionVersion = onlineConditionId2Versions.get(condition.getConditionId());

            // 如果不一致，代表已经有人对该条件提前提交了变更
            if (onlineConditionVersion != null && !onlineConditionVersion.getReleaseVersion().equals(conditionVersionBO.getPreviousReleaseVersion())) {
                throw CommonException.getDynamicException(ExceptionEnum.CONDITION_PREVIOUS_RELEASE_VERSION_NOT_MATCH, condition.getName());
            }
            return conditionVersionBO;
        }).collect(Collectors.toList());

        // 创建条件实体
        conditionDAO.saveOrUpdateBatch(newConditions);
        // 创建条件版本
        conditionVersionDAO.saveBatch(newConditionVersions);
    }

    /**
     * 查询条件列表，支持分页和条件查询
     *
     * @param query 查询条件
     * @return 条件分页列表结果
     */
    public Page<ConditionVersionBO> query(ConditionBO query, Pagination pagination) {
        Assert.notNull(query, "查询条件不能为空");

        Page<OConditionDO> pageResult = conditionDAO.lambdaQuery()
                .ne(OConditionDO::getStatus, ConditionStatus.DELETED)
                .eq(StringUtils.isNotBlank(query.getNamespaceId()), OConditionDO::getNamespaceId, query.getNamespaceId())
                .like(StringUtils.isNotBlank(query.getName()), OConditionDO::getName, query.getName())
                .page(PageUtil.build(pagination));

        Page<ConditionVersionBO> result = BeanUtil.createFromProperties(pageResult, Page.class);

        if (CollectionUtils.isEmpty(pageResult.getRecords())) {
            result.setRecords(new ArrayList<>());
            return result;
        }

        List<String> conditionIds = pageResult.getRecords().stream().map(OConditionDO::getConditionId).collect(Collectors.toList());
        var conditionId2ParamCondition = getParameterConditionVersions(conditionIds);

        List<ConditionVersionBO> conditions = pageResult.getRecords()
                .stream()
                .map(c -> {
                    ConditionVersionBO condition = BeanUtil.createFromProperties(c, ConditionVersionBO.class);
                    condition.setParameterConditionVersions(conditionId2ParamCondition.get(c.getConditionId()));
                    return condition;
                })
                .collect(Collectors.toList());
        result.setRecords(conditions);
        return result;
    }

    /**
     * 获取条件被引用的参数列表
     *
     * @param conditionIds
     * @return
     */
    private Map<String, List<ParameterConditionVersionBO>> getParameterConditionVersions(List<String> conditionIds) {
        if (CollectionUtils.isEmpty(conditionIds)) {
            return new HashMap<>();
        }

        var conditionId2ParamCondition = parameterConditionVersionDAO.lambdaQuery()
                .in(OParameterConditionVersionDO::getConditionId, conditionIds)
                .ne(OParameterConditionVersionDO::getStatus, VersionStatus.OUTDATED)
                .list()
                .stream()
                .map(i -> BeanUtil.createFromProperties(i, ParameterConditionVersionBO.class))
                .collect(Collectors.groupingBy(ParameterConditionVersionBO::getConditionId));

        // 填充参数名称
        setParameterKey(conditionId2ParamCondition);

        return conditionId2ParamCondition;
    }

    /**
     * 获取制定顺序条件名称的条件ID列表
     *
     * @param namespaceId    命名空间ID
     * @param conditionNames 条件名称列表
     * @return 条件ID列表
     */
    public List<String> getOrderedConditionIdsByName(String namespaceId, List<String> conditionNames) {
        if (CollectionUtils.isEmpty(conditionNames)) {
            return new ArrayList<>();
        }

        List<OConditionDO> conditions = conditionDAO.getByNames(namespaceId, conditionNames);

        Map<String, String> conditionName2Id = conditions.stream()
                .collect(Collectors.toMap(OConditionDO::getName, OConditionDO::getConditionId, (v1, v2) -> {
                    throw CommonException.getDynamicException(ExceptionEnum.CONDITION_NAME_DUPLICATE, v1);
                }));

        return conditionNames.stream()
                .map(conditionName2Id::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 获取指定的所有条件
     *
     * @param query
     * @return
     */
    public List<ConditionVersionBO> getAll(ConditionBO query) {
        var conditionId2Condition = conditionDAO.lambdaQuery()
                .eq(StringUtils.isNotBlank(query.getNamespaceId()), OConditionDO::getNamespaceId, query.getNamespaceId())
                .ne(OConditionDO::getStatus, ConditionStatus.DELETED)
                .list()
                .stream()
                .collect(Collectors.toMap(OConditionDO::getConditionId, i -> i));

        return conditionVersionDAO.lambdaQuery()
                .eq(StringUtils.isNotBlank(query.getNamespaceId()), OConditionVersionDO::getNamespaceId, query.getNamespaceId())
                .eq(OConditionVersionDO::getStatus, VersionStatus.RELEASED)
                .ne(OConditionVersionDO::getChangeType, ChangeType.DELETE)
                .list()
                .stream()
                .map(i -> {
                    ConditionVersionBO conditionVersion = BeanUtil.createFromProperties(i, ConditionVersionBO.class);
                    conditionVersion.setCondition(conditionId2Condition.get(i.getConditionId()));
                    return conditionVersion;
                })
                .collect(Collectors.toList());
    }

    public ConditionVersionBO getConditionDetailByConditionId(String conditionId) {
        OConditionDO condition = conditionDAO.getByConditionId(conditionId);
        if (condition == null) {
            throw new CommonException(ExceptionEnum.CONDITION_NOT_FOUND);
        }

        OConditionVersionDO conditionVersion = getOnlineConditionVersions(List.of(conditionId)).get(conditionId);

        if (conditionVersion == null) {
            throw new CommonException(ExceptionEnum.CONDITION_NOT_FOUND);
        }

        var conditionVersionBO = BeanUtil.createFromProperties(conditionVersion, ConditionVersionBO.class);
        conditionVersionBO.setCondition(condition);
        return conditionVersionBO;
    }

    /**
     * 获取条件对应的在线版本
     *
     * @param conditionIds 条件ID列表
     * @return 条件ID与条件版本的映射
     */
    public Map<String, OConditionVersionDO> getOnlineConditionVersions(List<String> conditionIds) {
        List<OConditionVersionDO> conditionVersionDOS = conditionVersionDAO.lambdaQuery()
                .in(OConditionVersionDO::getConditionId, conditionIds)
                .eq(OConditionVersionDO::getStatus, VersionStatus.RELEASED)
                .ne(OConditionVersionDO::getChangeType, ChangeType.DELETE)
                .list();
        return conditionVersionDOS.stream()
                .collect(Collectors.toMap(OConditionVersionDO::getConditionId, Function.identity()));
    }

    private void setParameterKey(Map<String, List<ParameterConditionVersionBO>> conditionId2ParamCondition) {
        List<String> parameterIds = conditionId2ParamCondition.values().stream()
                .flatMap(Collection::stream)
                .map(ParameterConditionVersionBO::getParameterId)
                .distinct()
                .collect(Collectors.toList());

        Map<String, OParameterDO> parameterId2ByParameter = getParameterByParameterId(parameterIds);

        conditionId2ParamCondition.forEach((k, v) -> {
            v.forEach(i ->
                    i.setParameterKey(parameterId2ByParameter.get(i.getParameterId()).getParameterKey()));
        });
    }

    private Map<String, OParameterDO> getParameterByParameterId(List<String> parameterIds) {
        return parameterDAO.lambdaQuery()
                .select(OParameterDO::getParameterId, OParameterDO::getParameterKey)
                .in(OParameterDO::getParameterId, parameterIds)
                .list()
                .stream()
                .collect(Collectors.toMap(OParameterDO::getParameterId, Function.identity(), (v1, v2) -> v1));
    }
}
