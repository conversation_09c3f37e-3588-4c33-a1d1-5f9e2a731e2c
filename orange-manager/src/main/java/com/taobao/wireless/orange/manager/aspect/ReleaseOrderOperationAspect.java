package com.taobao.wireless.orange.manager.aspect;

import com.taobao.unifiedsession.core.json.JSON;
import com.taobao.wireless.orange.common.annotation.OperationLog;
import com.taobao.wireless.orange.common.constant.enums.OperationStatus;
import com.taobao.wireless.orange.common.constant.enums.OperationType;
import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.dal.enhanced.dao.OReleaseOrderDAO;
import com.taobao.wireless.orange.dal.enhanced.dao.OReleaseOrderOperationDAO;
import com.taobao.wireless.orange.dal.enhanced.entity.OReleaseOrderDO;
import com.taobao.wireless.orange.dal.enhanced.entity.OReleaseOrderOperationDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 发布单操作记录切面
 *
 * <AUTHOR>
 */
@Aspect
@Order(20)
@Component
@Slf4j
public class ReleaseOrderOperationAspect {
    @Autowired
    private OReleaseOrderOperationDAO releaseOrderOperationDAO;
    @Autowired
    private OReleaseOrderDAO releaseOrderDAO;

    @Around("@annotation(com.taobao.wireless.orange.common.annotation.OperationLog)")
    public Object logOperation(ProceedingJoinPoint joinPoint) throws Throwable {
        OperationType operationType = ((MethodSignature) joinPoint.getSignature())
                .getMethod()
                .getAnnotation(OperationLog.class)
                .type();

        // 如果是新增则最后插入操作记录，由于新建完成后才能产生 releaseVersion
        if (operationType == OperationType.CREATE) {
            String releaseVersion = (String) joinPoint.proceed();
            createOperation(releaseVersion, operationType, null);
            return releaseVersion;
        }

        // 其他发布单操作记录
        Object[] args = joinPoint.getArgs();
        Long releaseOrderId = createOperation((String) args[0], operationType, args.length > 1 ? args[1] : null);

        try {
            Object result = joinPoint.proceed();
            updateStatus(releaseOrderId, OperationStatus.SUCCESS, null);
            return result;
        } catch (Throwable e) {
            updateStatus(releaseOrderId, OperationStatus.FAILED, e.getMessage());
            throw e;
        }
    }

    protected void updateStatus(Long id, OperationStatus status, String errorMessage) {
        var operation = new OReleaseOrderOperationDO();
        operation.setId(id);
        operation.setStatus(status);
        if (StringUtils.isNotBlank(errorMessage)) {
            operation.setResult(JSON.toJSONString(Map.entry("errorMessage", errorMessage)));
        }
        releaseOrderOperationDAO.updateById(operation);
    }

    private Long createOperation(String releaseVersion, OperationType operationType, Object params) {
        OReleaseOrderDO releaseOrder = releaseOrderDAO.getByReleaseVersion(releaseVersion);
        if (releaseOrder == null) {
            throw new CommonException(ExceptionEnum.RELEASE_ORDER_NOT_EXIST);
        }

        OReleaseOrderOperationDO operation = new OReleaseOrderOperationDO();
        operation.setReleaseVersion(releaseVersion);
        operation.setType(operationType);
        operation.setAppKey(releaseOrder.getAppKey());
        operation.setNamespaceId(releaseOrder.getNamespaceId());
        operation.setParams(params != null ? JSON.toJSONString(params) : null);
        operation.setStatus(OperationStatus.INIT);
        releaseOrderOperationDAO.save(operation);
        return operation.getId();
    }
}
