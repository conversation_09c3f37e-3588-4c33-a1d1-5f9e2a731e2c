package com.taobao.wireless.orange.manager.model;

import com.taobao.wireless.orange.dal.enhanced.entity.OParameterConditionVersionDO;
import com.taobao.wireless.orange.dal.enhanced.entity.OParameterDO;
import com.taobao.wireless.orange.dal.enhanced.entity.OParameterVersionDO;
import com.taobao.wireless.orange.dal.enhanced.entity.OReleaseOrderDO;
import lombok.Data;

import java.util.List;

@Data
public class ParameterBO extends OParameterDO {
    /**
     * 当前线上的参数版本信息
     */
    private OParameterVersionDO releasedParameterVersion;
    /**
     * 当前线上的参数条件信息
     */
    private List<OParameterConditionVersionDO> releasedParameterConditionVersions;
    /**
     * 发布中的发布单
     */
    private OReleaseOrderDO inPublishReleaseOrder;

    /**
     * 搜索关键字
     */
    private String keyword;
}
