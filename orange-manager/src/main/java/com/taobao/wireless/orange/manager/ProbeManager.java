package com.taobao.wireless.orange.manager;

import com.taobao.mtop.commons.utils.CollectionUtil;
import com.taobao.unifiedsession.core.json.JSON;
import com.taobao.wireless.orange.common.constant.enums.Available;
import com.taobao.wireless.orange.dal.enhanced.dao.ONamespaceVersionDAO;
import com.taobao.wireless.orange.dal.enhanced.dao.OProbeDAO;
import com.taobao.wireless.orange.dal.enhanced.dao.OResourceDAO;
import com.taobao.wireless.orange.dal.enhanced.entity.OIndexDO;
import com.taobao.wireless.orange.dal.enhanced.entity.ONamespaceVersionDO;
import com.taobao.wireless.orange.dal.enhanced.entity.OProbeDO;
import com.taobao.wireless.orange.dal.enhanced.entity.OResourceDO;
import com.taobao.wireless.orange.manager.model.ProbeContentItemBO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ProbeManager {
    @Autowired
    private IndexManager indexManager;

    @Autowired
    private ONamespaceVersionDAO namespaceVersionDAO;

    @Autowired
    private NamespaceVersionManager namespaceVersionManager;

    @Autowired
    private OProbeDAO probeDAO;

    @Autowired
    private OResourceDAO resourceDAO;

    /**
     * 生成应用的探针文件
     *
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void generate() {
        // 需要发布的应用列表
        var appKeys = getNeedPublishAppKeys();

        if (CollectionUtil.isEmpty(appKeys)) {
            return;
        }

        List<OProbeDO> newProbes = new ArrayList<>();
        for (String appKey : appKeys) {
            List<ONamespaceVersionDO> namespaceVersions = namespaceVersionManager.getAvailableNamespaceVersions(appKey);
            // 探针索引版本号即为最大的 namespaceChangeVersion
            String indexVersion = getMaxNamespaceChangeVersion(namespaceVersions);

            List<OIndexDO> indexDOs = indexManager.generate(appKey, namespaceVersions, indexVersion);
            if (CollectionUtil.isEmpty(indexDOs)) {
                continue;
            }

            OProbeDO probeDO = OProbeDO.builder()
                    .appKey(appKey)
                    .indexVersion(indexVersion)
                    .isAvailable(Available.Y)
                    .content(buildProbeContent(indexDOs))
                    .build();
            newProbes.add(probeDO);

            namespaceVersionDAO.lambdaUpdate()
                    .eq(ONamespaceVersionDO::getAppKey, appKey)
                    .isNull(ONamespaceVersionDO::getIndexVersion)
                    .le(ONamespaceVersionDO::getNamespaceChangeVersion, indexVersion)
                    .set(ONamespaceVersionDO::getIndexVersion, indexVersion)
                    .update();
        }

        createProbes(newProbes);

        // todo: publish to wmcc
    }

    /**
     * 新增探针记录
     *
     * @param newProbes 新增探针记录
     * @return
     */
    public void createProbes(List<OProbeDO> newProbes) {
        List<String> appKeys = newProbes.stream().map(OProbeDO::getAppKey).collect(Collectors.toList());

        // 将应用历史探针失效
        probeDAO.lambdaUpdate()
                .in(OProbeDO::getAppKey, appKeys)
                .set(OProbeDO::getIsAvailable, Available.N)
                .update();

        // 插入最新的探针记录
        probeDAO.saveBatch(newProbes);
    }

    /**
     * 获取需要发布的应用列表
     */
    private List<String> getNeedPublishAppKeys() {
        var namespaceVersions = namespaceVersionDAO.lambdaQuery()
                .isNull(ONamespaceVersionDO::getIndexVersion)
                .list();

        return namespaceVersions.stream().map(ONamespaceVersionDO::getAppKey).distinct().collect(Collectors.toList());
    }

    /**
     * 根据索引列表生成探针内容
     *
     * @param indices 索引列表
     * @return 探针内容
     */
    private String buildProbeContent(List<OIndexDO> indices) {
        var resourceIds = indices.stream().map(OIndexDO::getIndexResourceId).collect(Collectors.toList());
        Map<String, String> resourceId2Md5 = resourceDAO.lambdaQuery()
                .in(OResourceDO::getResourceId, resourceIds)
                .list()
                .stream()
                .collect(Collectors.toMap(OResourceDO::getResourceId, OResourceDO::getMd5));

        var contentItems = indices.stream()
                .sorted(Comparator.comparing(OIndexDO::getIndexVersion).reversed())
                .map(index -> ProbeContentItemBO.builder()
                        .baseVersion(index.getBaseIndexVersion())
                        .md5(resourceId2Md5.get(index.getIndexResourceId()))
                        .resourceId(index.getIndexResourceId())
                        .build()
                ).collect(Collectors.toList());
        return JSON.toJSONString(contentItems);
    }

    private String getMaxNamespaceChangeVersion(List<ONamespaceVersionDO> namespaceVersions) {
        return namespaceVersions.stream()
                .map(ONamespaceVersionDO::getNamespaceChangeVersion)
                .max(Comparator.naturalOrder())
                .get();
    }
}
