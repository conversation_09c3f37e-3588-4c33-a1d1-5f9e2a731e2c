package com.taobao.wireless.orange.user;


import com.alibaba.fastjson.JSON;
import com.taobao.wireless.orange.BaseTest;
import com.taobao.wireless.orange.common.model.User;
import com.taobao.wireless.orange.external.user.AmdpService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.Map;

public class AmdpServiceTest extends BaseTest {
    @Autowired
    private AmdpService amdpService;

    @Test
    public void queryUserByWorkNoList() {
        Map<String, User> stringUserMap = this.amdpService.queryUserByWorkNoList(Arrays.asList(DEFAULT_WORKER_ID));
        System.out.printf(JSON.toJSONString(stringUserMap));
    }
}