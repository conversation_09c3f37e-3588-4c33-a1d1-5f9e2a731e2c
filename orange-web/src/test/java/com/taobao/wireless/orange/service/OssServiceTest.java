package com.taobao.wireless.orange.service;

import com.taobao.wireless.orange.BaseTest;
import com.taobao.wireless.orange.external.OssService;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;


public class OssServiceTest extends BaseTest {
    @Autowired
    private OssService ossService;

    @Test
    @Ignore
    public void putObj() {
        this.ossService.uploadData("test", "o-config-testing", "test.json");
    }
}
