package com.taobao.wireless.orange.manager.model;

import com.google.protobuf.MessageOrBuilder;
import com.google.protobuf.util.JsonFormat;
import com.taobao.wireless.orange.BaseIntegrationTest;
import com.taobao.wireless.orange.common.model.proto.GrayConfigProto;
import com.taobao.wireless.orange.common.model.proto.ReleaseConfigProto;
import com.taobao.wireless.orange.external.OssService;
import com.taobao.wireless.orange.external.config.SwitchConfig;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

public class ConfigTest extends BaseIntegrationTest {

    @Autowired
    private OssService ossService;

    @Value("${orange.oss.bucketName}")
    public String bucketName;

    @Before
    public void setUp() throws Exception {
        SwitchConfig.protocolType = "protobuf";
    }

    @Test
    public void testSerializeAndDeserializeReleaseConfig() throws Exception {
        String resourceId = "0d7d142086e6451a9e793daacaecbfd7.bin";
        byte[] bytes = ossService.readData(bucketName, resourceId);
        try {
            MessageOrBuilder proto = ReleaseConfigProto.parseFrom(bytes);
            String jsonStr = JsonFormat.printer()
                    .includingDefaultValueFields()
                    .print(proto);
            Assert.assertEquals("{\n" +
                    "  \"schemaVersion\": \"1.0\",\n" +
                    "  \"namespace\": \"测试命名空间-c4b89e82\",\n" +
                    "  \"strategy\": \"FULL\",\n" +
                    "  \"type\": \"RELEASE\",\n" +
                    "  \"offlineParameters\": [],\n" +
                    "  \"conditions\": [{\n" +
                    "    \"id\": \"682f8457739f45ed91c3648aec03419d\",\n" +
                    "    \"expression\": {\n" +
                    "      \"operator\": \"AND\",\n" +
                    "      \"children\": [{\n" +
                    "        \"operator\": \"!\\u003d\",\n" +
                    "        \"children\": [],\n" +
                    "        \"key\": \"app_ver\",\n" +
                    "        \"value\": \"1.10.3\"\n" +
                    "      }],\n" +
                    "      \"key\": \"\",\n" +
                    "      \"value\": \"\"\n" +
                    "    }\n" +
                    "  }],\n" +
                    "  \"parameters\": [{\n" +
                    "    \"key\": \"enable_x\",\n" +
                    "    \"version\": \"2025052317031189900\",\n" +
                    "    \"valueType\": \"JSON\",\n" +
                    "    \"conditionalValues\": [{\n" +
                    "      \"conditionId\": \"682f8457739f45ed91c3648aec03419d\",\n" +
                    "      \"boolValue\": false\n" +
                    "    }],\n" +
                    "    \"defaultBoolValue\": true\n" +
                    "  }]\n" +
                    "}", jsonStr);
        } catch (Exception e) {
            Assert.assertNull(e);
        }
    }

    @Test
    public void testSerializeAndDeserializeGrayConfig() throws Exception {
        String resourceId = "5799362cf8174f44bcccfe95b485fbf5.bin";
        byte[] bytes = ossService.readData(bucketName, resourceId);
        try {
            MessageOrBuilder proto = GrayConfigProto.parseFrom(bytes);
            String jsonStr = JsonFormat.printer()
                    .includingDefaultValueFields()
                    .print(proto);
            Assert.assertEquals("{\n" +
                    "  \"schemaVersion\": \"1.0\",\n" +
                    "  \"namespace\": \"orange_test_ios\",\n" +
                    "  \"strategy\": \"FULL\",\n" +
                    "  \"type\": \"EXPERIMENT\",\n" +
                    "  \"orders\": [{\n" +
                    "    \"version\": \"2025051514552200332\",\n" +
                    "    \"offlineParameters\": [],\n" +
                    "    \"parameters\": [{\n" +
                    "      \"key\": \"k3_json\",\n" +
                    "      \"version\": \"2025051514552200332\",\n" +
                    "      \"valueType\": \"STRING\",\n" +
                    "      \"conditionalValues\": [],\n" +
                    "      \"defaultStringValue\": \"{\\n    \\\"a\\\": \\\"b\\\",\\n    \\\"c\\\": 1,\\n    \\\"d\\\": true\\n}\"\n" +
                    "    }]\n" +
                    "  }],\n" +
                    "  \"conditions\": []\n" +
                    "}", jsonStr);
        } catch (Exception e) {
            Assert.assertNull(e);
        }
    }
}
