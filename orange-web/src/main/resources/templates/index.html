<!doctype html>
<head>
    <meta charset="utf-8"/>
    <!--    <link rel="icon" href="/static/img/logo_v5.png">-->
    <meta name="viewport"
          content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover"/>
    <title>Orange-配置发布平台</title>

    <link rel="stylesheet"
          th:href="'https://' + ${assertDomain} + '/orange/orange-fe/' + ${version} + '/css/vendors.css'"/>
    <link rel="stylesheet"
          th:href="'https://' + ${assertDomain} + '/orange/orange-fe/' + ${version} + '/css/main.css'"/>
</head>
<body>
<div id="ice-container"></div>
<script type="text/javascript">
  window._DATA_ = {
    userId: "[[${empId}]]",
    userName: "[[${userNickName}]]",
    isAdmin: [[${isAdmin}]],
    wholeProcess: [[${needWholeProcess}]],
    emailPrefix: "[[${emailPrefix}]]",
    tips: "[[${announcement}]]"
  }
</script>
<script>
  !(function () {
    var a = window.__ICE_APP_CONTEXT__ || {};
    var b = {
      appData: null,
      loaderData: {},
      routePath: "",
      matchedIds: [],
      documentOnly: true,
      renderMode: "CSR",
    };
    for (var k in a) {
      b[k] = a[k];
    }
    window.__ICE_APP_CONTEXT__ = b;
  })();
</script>
<script src="//g.alicdn.com/xfeedback/xfeedback/2.15.1/api.js"></script>

<script th:src="'https://' + ${assertDomain} + '/orange/orange-fe/' + ${version} + '/js/data-loader.js'"></script>
<script th:src="'https://' + ${assertDomain} + '/orange/orange-fe/' + ${version} + '/js/vendors.js'"></script>
<script th:src="'https://' + ${assertDomain} + '/orange/orange-fe/' + ${version} + '/js/main.js'"></script>
</body>
</html>
