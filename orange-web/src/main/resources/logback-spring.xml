<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- https://github.com/spring-projects/spring-boot/blob/v2.5.12/spring-boot-project/spring-boot/src/main/resources/org/springframework/boot/logging/logback/defaults.xml -->
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <property name="APP_NAME" value="orange"/>
    <property name="LOG_PATH" value="${user.home}/${APP_NAME}/logs"/>
    <property name="SYS_LOG_PATH" value="${LOG_PATH}/sys"/>
    <property name="LOG_FILE" value="${LOG_PATH}/application.log"/>

    <appender name="APPLICATION"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_FILE}</file>
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern><![CDATA[
            [%d{yyyy-MM-dd HH:mm:ss.SSS} %5p]%X{EAGLEEYE_TRACE_ID}|%c{2}|%m%n
            ]]></pattern>
        </layout>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_FILE}.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>50MB</maxFileSize>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <appender name="apiAsyncAppender" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="apiAppender"/>
    </appender>

    <appender name="apiAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${SYS_LOG_PATH}/api.log</file>
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern>%m%n</pattern>
        </layout>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${SYS_LOG_PATH}/api.log.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>500MB</maxFileSize>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <root level="ERROR">
        <appender-ref ref="APPLICATION"/>
    </root>

    <logger name="api" level="INFO" additivity="false">
        <appender-ref ref="apiAsyncAppender"/>
    </logger>
</configuration>
