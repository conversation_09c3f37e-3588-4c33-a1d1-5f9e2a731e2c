project.name=orange
server.port=7001
management.server.port=7002

spring.hsf.group=HSF
spring.hsf.version=$hsf.version
spring.hsf.timeout=$hsf.timeout

# spring.diamond.data-id=com.taobao.middleware:test.properties

spring.tair.user-name=$user-name
spring.tair.dynamic-config=true

spring.eagleeye.mdc-updater=slf4j
spring.eagleeye.enabled=true

spring.tddl.app=$tddl.app
spring.tddl.sharding=false

spring.metaq.producer.producer-group=panodra-boot-metaq-sample
#spring.metaq.consumer.consumer-group=CID-ORANGE
#spring.metaq.consumer.topic=wmcc_diamond_beta_publish
#spring.metaq.consumer.subExpression=*
#spring.metaq.consumer.message-listener-ref=metaqMessageListener

spring.metaq.consumers[0].consumer-group=CID-ORANGE-WMCC
spring.metaq.consumers[0].topic=wmcc_diamond_beta_publish
spring.metaq.consumers[0].sub-expression=*
spring.metaq.consumers[0].message-listener-ref=metaqMessageListener
spring.buc.app-name=union-data-op-web

# BUC
spring.buc.app-code=015854409fa049d3bb5cfb70cd0706d0
spring.buc.filter.url-patterns=/*
spring.buc.login-env=online

spring.schedulerx2.domainName=schedulerx2.taobao.net
spring.schedulerx2.namespace=system_namespace
spring.schedulerx2.groupId=orange
spring.schedulerx2.appKey=WPtgg6giacpteU5cSUhMscw

logging.level.root=ERROR

amdp.hsf.group=HSF
amdp.hsf.version=1.0.0
amdp.app.key=orange
amdp.app.secret=test
amdp.api.emp.id=1801
amdp.api.emp.search.id=1531

mtl.accessKey=orange
mtl.accessSecret=ade2d56eca6f1fbba90033dec47dd908
mtl.endpoint=https://open.mtl4.alibaba-inc.com