package com.taobao.wireless.orange.web.controller;

import com.taobao.wireless.orange.common.model.PaginationResult;
import com.taobao.wireless.orange.common.model.Result;
import com.taobao.wireless.orange.service.ExperimentService;
import com.taobao.wireless.orange.service.model.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "实验管理接口")
@RestController
@RequestMapping("/api/experiments")
public class ExperimentController {

    @Autowired
    private ExperimentService experimentService;

    @ApiOperation("查询实验列表")
    @GetMapping
    public PaginationResult<ExperimentDTO> query(ExperimentQueryDTO query) {
        return null;
    }

    @ApiOperation("新建实验")
    @PostMapping
    public Result<Long> create(@RequestBody ExperimentCreateDTO experiment) {
        return null;
    }

    @ApiOperation("查询指定实验版本实验详情")
    @GetMapping("/{id}/versions/{version}")
    public Result<ExperimentDTO> getVersion(
            @PathVariable("id") Long id,
            @PathVariable("version") Long version) {
        return null;
    }

    @ApiOperation("修改实验")
    @PutMapping("/{id}")
    public Result<Boolean> update(
            @PathVariable("id") Long id,
            @RequestBody ExperimentUpdateDTO experiment) {
        return null;
    }

    @ApiOperation("启动实验")
    @PostMapping("/{id}/start")
    public Result<Boolean> start(@PathVariable("id") Long id) {
        return null;
    }

    @ApiOperation("停止实验")
    @PostMapping("/{id}/stop")
    public Result<Boolean> stop(@PathVariable("id") Long id) {
        return null;
    }

    @ApiOperation("查询实验版本列表")
    @GetMapping("/{id}/versions")
    public Result<List<ReleaseOrderDTO>> listVersions(@PathVariable("id") Long id) {
        return null;
    }
}
