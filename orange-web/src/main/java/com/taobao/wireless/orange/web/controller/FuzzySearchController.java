package com.taobao.wireless.orange.web.controller;

import com.taobao.wireless.orange.common.model.Pagination;
import com.taobao.wireless.orange.common.model.PaginationResult;
import com.taobao.wireless.orange.common.model.Result;
import com.taobao.wireless.orange.common.model.User;
import com.taobao.wireless.orange.external.mtl.MtlService;
import com.taobao.wireless.orange.external.mtl.model.MtlModule;
import com.taobao.wireless.orange.service.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "模糊搜索接口")
@RestController
@RequestMapping("/api/fuzzy-search")  // 复数形式
@Slf4j
public class FuzzySearchController {

    @Autowired
    private MtlService mtlService;

    @Autowired
    private UserService userService;

    @ApiOperation("模糊搜索用户")
    @GetMapping("/users")
    public Result<List<User>> users(@RequestParam("keyword") String keyword) {
        return this.userService.fuzzySearchUserByKeyword(keyword);
    }

    @ApiOperation("模糊搜索摩天轮模块")
    @GetMapping("/modules")
    public PaginationResult<MtlModule> modules(@RequestParam("appKey") String appKey,
                                               @RequestParam("keyword") String keyword,
                                               @RequestParam(defaultValue = "1") Integer page,
                                               @RequestParam(defaultValue = "10") Integer size) {
        return this.mtlService.queryModules(appKey, keyword, new Pagination(page, size));
    }
}
