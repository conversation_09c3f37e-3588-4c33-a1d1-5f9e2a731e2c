package com.taobao.wireless.orange.web.aspect;

import com.taobao.eagleeye.EagleEye;
import com.taobao.unifiedsession.core.json.JSON;
import com.taobao.wireless.orange.common.constant.enums.LogType;
import com.taobao.wireless.orange.common.constant.enums.ProtocolType;
import com.taobao.wireless.orange.common.model.BaseResult;
import com.taobao.wireless.orange.common.model.LogParam;
import com.taobao.wireless.orange.common.thread.OThreadContext;
import com.taobao.wireless.orange.common.thread.OThreadContextHolder;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Controller层日志切面
 * 记录请求的URL、方法、参数、响应结果、执行时间等信息
 *
 * <AUTHOR>
 */
@Aspect
@Component
@Order(5)
@Slf4j(topic = "api")
public class ControllerLogAspect {

    /**
     * 定义切点：所有Controller类的所有方法
     */
    @Pointcut("execution(* com.taobao.wireless.orange.web.controller.*.*(..))")
    public void controllerPointcut() {
    }

    /**
     * 环绕通知：记录请求的各种信息
     */
    @Around("controllerPointcut()")
    public Object logAround(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        String startTimeStr = formatDate(new Date(startTime));

        // 获取请求信息
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            return joinPoint.proceed();
        }

        HttpServletRequest request = attributes.getRequest();
        String requestURI = request.getRequestURI();
        String httpMethod = request.getMethod();
        String localIp = request.getLocalAddr();

        // 获取方法签名
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        // 获取请求参数
        Map<String, Object> requestParams = getRequestParams(joinPoint, method);

        LogParam logParam = LogParam.builder()
                .type(LogType.HTTP)
                .protocolType(ProtocolType.HTTP)
                .env(System.getenv("envSign"))
                .system("orange")
                .localIp(localIp)
                .traceId(EagleEye.getTraceId())
                .rpcId(EagleEye.getRpcId())
                .input(JSON.toJSONString(requestParams))
                .method(httpMethod)
                .startTime(startTimeStr)
                .path(requestURI)
                .build();

        // 获取用户信息
        OThreadContext threadContext = OThreadContextHolder.get();
        if (threadContext != null) {
            logParam.setWorkerId(threadContext.getWorkerId());
            logParam.setWorkerName(threadContext.getWorkerName());
        }

        try {
            Object result = joinPoint.proceed();
            if (result instanceof BaseResult baseResult) {
                logParam.setSuccess(baseResult.isSuccess());
                logParam.setCode(baseResult.getCode());
            } else {
                logParam.setSuccess(true);
            }
            logParam.setOutput(JSON.toJSONString(result));
            return result;
        } catch (Throwable e) {
            logParam.setSuccess(false);
            throw e;
        } finally {
            long endTime = System.currentTimeMillis();
            logParam.setCostTime(endTime - startTime);
            logParam.setEndTime(formatDate(new Date(endTime)));

            if (BooleanUtils.isTrue(logParam.getSuccess())) {
                log.info(JSON.toJSONString(logParam));
            } else {
                log.error(JSON.toJSONString(logParam));
            }
        }
    }

    /**
     * 获取请求参数
     */
    private Map<String, Object> getRequestParams(ProceedingJoinPoint joinPoint, Method method) {
        Map<String, Object> requestParams = new HashMap<>();

        // 获取方法参数
        Object[] args = joinPoint.getArgs();
        Parameter[] parameters = method.getParameters();
        Annotation[][] parameterAnnotations = method.getParameterAnnotations();

        for (int i = 0; i < parameters.length; i++) {
            // 忽略HttpServletRequest等内置对象
            if (args[i] instanceof HttpServletRequest) {
                continue;
            }

            // 检查是否有@RequestBody注解
            boolean isRequestBody = false;
            for (Annotation annotation : parameterAnnotations[i]) {
                if (annotation instanceof RequestBody) {
                    isRequestBody = true;
                    break;
                }
            }

            // 对于@RequestBody注解的参数，直接添加整个对象
            if (isRequestBody) {
                requestParams.put("requestBody", args[i]);
            } else {
                // 对于普通参数，添加参数名和值
                requestParams.put(parameters[i].getName(), args[i]);
            }
        }

        return requestParams;
    }

    /**
     * 格式化日期为字符串
     */
    private String formatDate(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        return sdf.format(date);
    }
}
